{"common.action": "Acción", "common.actions": "Acciones", "common.add": "<PERSON><PERSON><PERSON>", "common.admin": "Admin", "common.amount": "Cantidad", "common.anonymous": "<PERSON><PERSON><PERSON>", "common.any-chain": "", "common.any-token": "Cualquier token", "common.blockchain": "Cadena de bloques", "common.cancel": "<PERSON><PERSON><PERSON>", "common.checking": "Comprobando", "common.clear-all": "<PERSON><PERSON><PERSON> todo", "common.confirm": "Confirme", "common.confirmation": "Confirmación", "common.connect-wallet-to-login": "Conecta tu cartera para iniciar sesión", "common.contact-us": "Póngase en contacto con nosotros", "common.copy": "Copiar", "common.created-at": "Creado en", "common.delete": "Bo<PERSON>r", "common.edit": "<PERSON><PERSON>", "common.email": "Correo electrónico", "common.enter-email": "Introduce el correo", "common.enter-phone-number": "Introduce el número de teléfono", "common.error": "Error", "common.go-back": "Regresar", "common.hide-filter": "Ocultar filtro", "common.learn-more-about": "Obtenga más información sobre", "common.loading": "Cargando", "common.member": "Miembro", "common.name": "Nombre", "common.no-access": "Sin acceso", "common.no-data-available": "No hay datos disponibles", "common.no-image": "", "common.owner": "<PERSON><PERSON>", "common.owners": "<PERSON><PERSON>", "common.phone-number": "número de teléfono", "common.pick-date": "Elige una fecha", "common.please-check": "Por favor, compruebe", "common.please-wait": "<PERSON><PERSON><PERSON>, por favor", "common.privacy-policy": "Política de privacidad", "common.recipient": "Beneficiario", "common.refresh": "<PERSON><PERSON><PERSON><PERSON>", "common.remove": "Eliminar", "common.role": "Rol", "common.save": "Guardar", "common.search-placeholder": "Búsqueda rápida", "common.select": "Seleccione", "common.select-chain": "Seleccione la cadena", "common.select-status": "Seleccione el estado", "common.select-token": "Seleccione el token", "common.send": "Enviar", "common.send-token": "Enviar token", "common.show-filter": "Mostrar filtro", "common.status.active": "Activo", "common.status.expired": "<PERSON><PERSON><PERSON><PERSON>", "common.status.inactive": "", "common.status.pending": "Pendiente", "common.status.text": "Estado", "common.status.title": "Estado", "common.terms": "Térm<PERSON>s", "common.time": "<PERSON><PERSON>", "common.time-utc8": "Hora (UTC+8)", "common.token": "Símbolo", "common.tx-hash": "Hash fiscal", "common.tx-status-failed": "<PERSON><PERSON>", "common.tx-status-pending": "Pendiente", "common.tx-status-success": "Éxito", "common.update": "Actualización", "common.user": "Usuario", "common.wallet": "<PERSON><PERSON><PERSON> de cartera<PERSON>", "common.wallet-address": "Dirección del monedero", "common.welcome": "¡Bienvenida!", "error.cant-find-user": "Lo siento, no podemos encontrar a este usuario.", "error.general-error": "Ha ocurrido un error. Código: ", "error.no-access": "No tienes acceso. Utiliza una dirección de monedero verificada para iniciar sesión.", "error.try-again": "Se ha producido un error, inténtalo de nuevo más tarde.", "error.user-not-found": "No se ha encontrado el usuario.", "kgauth.change.change-email": "Cambiar correo electrónico", "kgauth.change.change-phone-number": "Cambiar número de teléfono", "kgauth.change.email-different": "El nuevo correo electrónico debe ser diferente del anterior", "kgauth.change.email-exists": "El correo electrónico ya existe", "kgauth.change.email-update-failed": "No se pudo actualizar el correo electrónico. Vuelve a intentarlo más tarde.", "kgauth.change.new-email": "Nuevo correo electrónico", "kgauth.change.new-phone-number": "Nuevo número de teléfono", "kgauth.change.old-email": "Tu correo electrónico anterior es", "kgauth.change.old-phone-number": "Tu número de teléfono anterior es", "kgauth.change.password-input": "Contraseña", "kgauth.change.password-input2": "Confirmar con<PERSON>", "kgauth.change.password-input-hint1": "Al menos 12 dígitos", "kgauth.change.password-input-hint2": "<PERSON><PERSON><PERSON> may<PERSON> y minúsculas, números y letras", "kgauth.change.password-mismatch": "Las contraseñas deben coincidir", "kgauth.change.password-title": "Cambiar contraseña", "kgauth.change.phone-duplicate": "El nuevo número de teléfono debe ser diferente al anterior", "kgauth.change.phone-exists": "El número de teléfono ya existe", "kgauth.change.verify-email": "Verificar correo electrónico", "kgauth.change.verify-phone-number": "Verificar número de teléfono", "kgauth.common.accept": "Aceptar", "kgauth.common.change-email-address": "cambiar dirección de correo electrónico", "kgauth.common.change-password": "cambiar contraseña", "kgauth.common.change-phone-number": "cambiar número de teléfono", "kgauth.common.continue": "<PERSON><PERSON><PERSON><PERSON>", "kgauth.common.decline": "Declinación", "kgauth.common.done": "<PERSON><PERSON>", "kgauth.common.link-line-account": "vincular cuenta LINE", "kgauth.common.loading": "Procesamiento de datos en curso. Por favor, no cierre esta ventana.", "kgauth.common.login": "In<PERSON><PERSON>", "kgauth.common.oauth-login": "inicio de sesi<PERSON>", "kgauth.common.resend": "Reenviar", "kgauth.common.retry": "Reintentar", "kgauth.common.seconds": "segundos", "kgauth.common.verify-email-address": "verificar la dirección de correo electrónico", "kgauth.common.verify-phone-number": "verificar número de teléfono", "kgauth.errors.common": "¡Algo salió mal!", "kgauth.errors.common-retry": "Algo salió mal. Vuelva a intentarlo más tarde.", "kgauth.errors.invalid-email": "Correo electrónico no válido", "kgauth.errors.invalid-otp": "Código no válido. Por favor, inténtelo de nuevo.", "kgauth.errors.invalid-phone": "Número de teléfono no válido", "kgauth.errors.otp-digit": "La OTP debe tener 6 dígitos", "kgauth.errors.password-required": "Contrase<PERSON>.", "kgauth.errors.rate-limit": "Se ha superado el límite de velocidad. Vuelve a intentarlo más tarde.", "kgauth.errors.return-to-wallet-in": "Volver a la billetera en", "kgauth.errors.return-to-wallet-now": "Regresa a la billetera ahora", "kgauth.errors.token-expired": "Su sesión ha caducado. Vuelva a iniciar sesión para continuar.", "kgauth.errors.update-phone": "No se pudo actualizar el número de teléfono. Vuelve a intentarlo más tarde.", "kgauth.forgot.backup-seed": "Frase inicial de respaldo", "kgauth.forgot.desc": "Importante: Si haces clic en «Aceptar» para restablecer la contraseña, borraremos la información de activos de tu cuenta al mismo tiempo. No te preocupes, puedes importar tu activo mediante una frase inicial más adelante. Pero te recomendamos que primero hagas una copia de seguridad de la frase inicial.", "kgauth.forgot.password": "Contraseña olvidada", "kgauth.forgot.reset": "OK. Cerraré sesión y restableceré la contraseña.", "kgauth.forgot.step1": "Cerrar sesión en la aplicación", "kgauth.forgot.step2": "Iniciar sesi<PERSON> de nuevo", "kgauth.forgot.step3": "Haz clic en «He olvidado mi contraseña» y sigue las guías", "kgauth.forgot.step-title": "Haga clic en «Aceptar» y siga los pasos", "kgauth.forgot.sub-title": "Para continuar, cierra sesión para que podamos ayudarte a restablecer la contraseña.", "kgauth.login.email": "Correo electrónico", "kgauth.login.input-required": "Se requiere información de inicio de sesión/registro.", "kgauth.login.or": "o", "kgauth.login.phone": "Teléfono", "kgauth.login.signin-sub-title": "para continuar", "kgauth.login.signin-title": "Iniciar <PERSON><PERSON>/ Registrarse en KryptoGo", "kgauth.login.with-google": "Iniciar se<PERSON><PERSON> con Google", "kgauth.oauth-callback.authorize-description": "Se solicitan los siguientes permisos. Revisa y asegúrate de que aceptas compartir esta información confidencial con este sitio o aplicación", "kgauth.oauth-callback.authorize-item-0": "Vea la información de su cuenta KryptoGO, incluido su correo electrónico, número de teléfono y nombre de usuario.", "kgauth.oauth-callback.authorize-item-1": "Acceda a las direcciones de su billetera y vea sus activos digitales.", "kgauth.oauth-callback.authorize-item-2": "Ver su historial de transacciones.", "kgauth.oauth-callback.authorize-item-3": "", "kgauth.oauth-callback.authorize-scope-and": "y", "kgauth.oauth-callback.authorize-scope-asset": "activos, NFT, transacciones", "kgauth.oauth-callback.authorize-scope-chatroom": "sala de chat", "kgauth.oauth-callback.authorize-scope-edit": "editar", "kgauth.oauth-callback.authorize-scope-notification": "notificaciones", "kgauth.oauth-callback.authorize-scope-order": "pedidos", "kgauth.oauth-callback.authorize-scope-read": "vista", "kgauth.oauth-callback.authorize-scope-token": "simbólico", "kgauth.oauth-callback.authorize-scope-transaction": "transacciones", "kgauth.oauth-callback.authorize-scope-user": "Información del usuario", "kgauth.oauth-callback.authorize-scope-vault": "<PERSON><PERSON><PERSON><PERSON>", "kgauth.oauth-callback.authorize-scope-wallet": "billeteras", "kgauth.oauth-callback.authorize-scope-your": "vuestro", "kgauth.oauth-callback.subtitle": "Una aplicación quiere acceder a su cuenta de KryptoGO", "kgauth.oauth-callback.title": "Autorizar la aplicación", "kgauth.password.enter-password": "Ingresa tu contraseña", "kgauth.password.forget-password": "¿Olvidó la contraseña?", "kgauth.password.invalid": "Contraseña no válida. Por favor, inténtelo de nuevo.", "kgauth.password.sub-title": "Para acceder a los activos de su monedero, utilice su contraseña para restaurar el monedero desde la copia de seguridad.", "kgauth.success.email": "¡Correo electrónico verificado correctamente!", "kgauth.success.email-updated": "¡El correo electrónico se actualizó correctamente!", "kgauth.success.password-updated": "¡La contraseña se actualizó correctamente!", "kgauth.success.phone": "¡El número de teléfono se verificó correctamente!", "kgauth.success.phone-updated": "¡El teléfono se actualizó correctamente!", "kgauth.verify.enter-digit": "Introduce el código de 6 dígitos que has recibido para", "kgauth.verify.not-receive-code": "¿No has recibido el código?", "kgauth.verify.title": "Hemos enviado un código a", "kgform.common.back-to-home": "Volver a Inicio", "kgform.common.change": "Cambiar", "kgform.common.next-page": "<PERSON><PERSON><PERSON><PERSON> sigu<PERSON>e", "kgform.common.previous-page": "Página anterior", "kgform.common.resend": "Reenviar", "kgform.common.start": "Empezar", "kgform.common.submit": "Enviar", "kgform.common.verify": "Verificar", "kgform.errors.addres.in-use": "La dirección ya está en uso.", "kgform.errors.address.in-use": "La dirección ya está en uso.", "kgform.errors.address.invalid-format": "Formato de dirección no válido.", "kgform.errors.birth-date": "<PERSON><PERSON> tener al menos 18 años.", "kgform.errors.code.invalid": "Código no válido.", "kgform.errors.code.invalid-format": "El código OTP debe tener 6 dígitos.", "kgform.errors.email.in-use": "El correo electrónico ya está en uso.", "kgform.errors.email.invalid-format": "Formato de correo electrónico no válido.", "kgform.errors.file-upload.file-too-large": "El tamaño del archivo es demasiado grande.", "kgform.errors.form-validation-error": "Algunos campos no están rellenados correctamente.", "kgform.errors.idIssue-date": "Indique la fecha de emisión completa.", "kgform.errors.id-number": "Introduce el número de identificación correcto.", "kgform.errors.max-length": "La longitud máxima es de {maxLength}caracteres", "kgform.errors.no-client-id": "El ID de cliente de OAuth no es válido.", "kgform.errors.oauth-login-error": "No se pudo iniciar sesión.", "kgform.errors.phone.in-use": "El número de teléfono ya está en uso.", "kgform.errors.phone.invalid-format": "Formato de número de teléfono no válido.", "kgform.errors.privacy-policy": "Acepta la Política de privacidad y seguridad de datos personales", "kgform.errors.required": "Este campo es obligatorio", "kgform.errors.signature.empty": "Por favor, firme su nombre completo.", "kgform.errors.signature.invalid": "Firma no válida.", "kgform.errors.something-went-wrong": "Se ha producido un error. Inténtalo de nuevo más tarde.", "kgform.errors.submission-error": "Error de envío. Inténtelo de nuevo más tarde.", "kgform.errors.too-many-request": "De<PERSON><PERSON>das solicitudes. Inténtelo de nuevo más tarde.", "kgform.errros.code.invalid": "Código no válido.", "kgform.form.address.connect": "Conéctese", "kgform.form.address.disconnect": "Desconectar", "kgform.form.email.dialog.description": "El código de verificación se ha enviado a {email}", "kgform.form.email.dialog.title": "Verificación de correo electrónico", "kgform.form.email.in-use": "El correo electrónico ya está en uso.", "kgform.form.file-upload.choose-file": "Elige un archivo", "kgform.form.file-upload.start-over": "Comenzar de nuevo", "kgform.form.login-and-start": "Iniciar se<PERSON><PERSON> y empezar", "kgform.form.phone.dialog.description": "El código de verificación se ha enviado a {phone}", "kgform.form.phone.dialog.title": "Verificación telefónica", "kgform.form.signature.clear": "Bo<PERSON>r", "kgform.form.signature.dialog.title": "<PERSON><PERSON>", "kgform.form.signature.placeholder": "Haga clic aquí para firmar con su nombre completo", "kgform.form.signature.save": "Guardar", "kgform.form.successfully-verirfied": "Verificado correctamente.", "kgform.form.upload-placeholder": "Haga clic o arrastre para cargar archivos", "kgform.index.buy-crypto": "Comp<PERSON> crip<PERSON>", "kgform.index.login": "In<PERSON><PERSON>", "kgform.index.register": "Registrarse", "kgstore.checkout.cta": "He realizado el pago", "kgstore.checkout.customer-info": "Información del cliente", "kgstore.checkout.error-create-order": "No se pudo crear el pedido.", "kgstore.checkout.order-correct-text": "Una vez que se haya creado un pedido, puede realizar el pago dentro del límite de tiempo especificado.", "kgstore.checkout.order-correct-title": "¿Pedido correcto?", "kgstore.checkout.order-summary": "Resumen del pedido", "kgstore.checkout.rate-updated-text": "El artículo que has seleccionado se ha actualizado con la nueva tarifa. Por favor, inténtalo de nuevo.", "kgstore.checkout.rate-updated-title": "Nueva tarifa actualizada", "kgstore.checkout.receiving-wallet": "Billetera receptora", "kgstore.checkout.title": "Checkout", "kgstore.checkout.toast": "La fecha límite de pago será {time}después de confirmar el pedido.", "kgstore.checkout.transfer-fund": "Transfiera fondos a ", "kgstore.checkout.will-receive": "Recibirás", "kgstore.common.about": "Acerca de", "kgstore.common.back": "Atrás", "kgstore.common.bank-transfer": "Transferencia bancaria", "kgstore.common.buy": "<PERSON><PERSON><PERSON>", "kgstore.common.cancel": "<PERSON><PERSON><PERSON>", "kgstore.common.confirm": "Confirmar", "kgstore.common.copy-success": "¡Copia exitosa!", "kgstore.common.email": "Correo electrónico", "kgstore.common.error": "Algo salió mal. Vuelva a intentarlo más tarde.", "kgstore.common.error-get-wallet": "No se pudo obtener la información del monedero.", "kgstore.common.kyc-status.pending": "Pendiente", "kgstore.common.kyc-status.processing": "Procesamiento", "kgstore.common.kyc-status.rejected": "<PERSON><PERSON><PERSON><PERSON>", "kgstore.common.kyc-status.unverified": "Sin verificar", "kgstore.common.kyc-status.verified": "Verificado", "kgstore.common.limit": "Límite", "kgstore.common.login-now": "Inicie se<PERSON><PERSON> ahora", "kgstore.common.login-order-desc": "Inicie sesión para realizar un pedido", "kgstore.common.login-order-title": "Inicie sesión para realizar un pedido", "kgstore.common.next": "Próxima", "kgstore.common.payment-method": "Método de pago", "kgstore.common.phone": "Teléfono", "kgstore.common.rate": "<PERSON><PERSON><PERSON>", "kgstore.common.recaptcha-error": "No se pudo verificar Google reCAPTCHA. Actualiza la página web o ponte en contacto con nuestro equipo de asistencia para obtener ayuda.", "kgstore.common.submit": "Enviar", "kgstore.common.subtotal": "Subtotal", "kgstore.common.total": "Total", "kgstore.common.try-again": "Inténtalo de nuevo", "kgstore.login.back": "Atrás", "kgstore.login.check-email-code": "Comprueba tu correo electrónico para recibir el código OTP.", "kgstore.login.continue": "<PERSON><PERSON><PERSON><PERSON>", "kgstore.login.email-required": "El correo electrónico es obligatorio", "kgstore.login.otp-message": "La OTP debe tener 6 dígitos", "kgstore.login.phone-required": "El número de teléfono es obligatorio", "kgstore.login.resend": "Reenviar", "kgstore.login.success": "Inicio de sesión exitoso. Redirigiendo, por favor espera.", "kgstore.login.title": "Inicia sesión/Regístrate en KryptoGO para continuar", "kgstore.login.welcome": "¡Bienvenido!", "kgstore.menu.orders": "Pedidos", "kgstore.menu.product": "Producto", "kgstore.menu.profile": "Perfil", "kgstore.order.active-title": "Activo", "kgstore.order.attachments.count": "{count}Expedientes", "kgstore.order.attachments.title": "<PERSON><PERSON><PERSON>", "kgstore.order.cancelled-time": "<PERSON><PERSON> cancelada", "kgstore.order.cancel-toast": "El propietario ha cancelado el pedido{orderId}, con un pago total de{cost}.\n\nSi ya has realizado el pago, ponte en contacto con el propietario lo antes posible para solicitar un reembolso.", "kgstore.order.cancel-toast-cta": "Verifique la información de contacto", "kgstore.order.delivered-time": "Tiempo de entrega", "kgstore.order.error-get-order": "No se pudieron recuperar los pedidos activos", "kgstore.order.error-get-order-details": "No se pudieron recuperar los detalles del pedido", "kgstore.order.history-title": "Historia", "kgstore.order.no-order": "Aún no tienes un pedido activo o completado", "kgstore.order.no-order-history": "Aún no se ha completado el pedido.", "kgstore.order.order-created": "Pedido c<PERSON>o", "kgstore.order.payment-time": "Plazo de pago", "kgstore.order.receiving-wallet": "Billetera receptora", "kgstore.order.shipment-time": "Tiempo de envío", "kgstore.order.shipment-tx-hash": "Hash de transacción", "kgstore.order.shipping-time": "Tiempo de envío", "kgstore.order.status-awaiting-confirmation": "En espera de confirmación", "kgstore.order.status-awaiting-shipment": "En espera de envío", "kgstore.order.status-canceled": "Cancelado", "kgstore.order.status-delivered": "<PERSON><PERSON><PERSON>", "kgstore.order.status-shipping": "Envío", "kgstore.order.status-unpaid": "No remunerado", "kgstore.order.unpaid-toast": "Realice el pago {order}antes{time}.", "kgstore.order.view-more": "<PERSON>er más", "kgstore.order.view-order-detail": "Ver detalles del pedido", "kgstore.order.view-receipt": "Ver recibo", "kgstore.payment.desc": "Sube al menos 1 captura de pantalla, imagen o cualquier otro archivo adjunto para demostrar tu pago.", "kgstore.payment.error-attachment": "No se pudieron cargar los archivos adjuntos", "kgstore.payment.error-file-size": "El tamaño del archivo es demasiado grande para subirlo.", "kgstore.payment.error-file-type": "Tipo de archivo no válido. Solo se permiten png, jpg, jpeg y webp.", "kgstore.payment.error-max-upload": "Solo se permiten 10 archivos adjuntos.", "kgstore.payment.error-upload": "No se pudieron cargar los detalles del pago", "kgstore.payment.file-restrict": "Deja aquí png/jpg/jpeg/heic/heif\nEl límite de tamaño del archivo es de 10 MB\nRelación de imagen: 300 x 8000 px", "kgstore.payment.status-awaiting-refund": "En espera de reembolso", "kgstore.payment.status-paid": "<PERSON><PERSON>", "kgstore.payment.status-refunded": "Reembolsado", "kgstore.payment.success-upload": "¡Los detalles del pago se cargaron correctamente!", "kgstore.payment.title": "Cargar archivo adjunto", "kgstore.payment.upload-and-inform": "Subir e informar al vendedor", "kgstore.payment.x-files-uploaded": "Archivos subidos", "kgstore.product.account-name": "Nombre de la cuenta", "kgstore.product.account-number": "Número de cuenta", "kgstore.product.bank-name": "Nombre del banco", "kgstore.product.branch-name": "<PERSON>", "kgstore.product.buy-now": "<PERSON><PERSON><PERSON> ahora", "kgstore.product.cta-need-kyc": "Aplica KYC para continuar", "kgstore.product.cta-sign-in": "Inicia sesión para comprar", "kgstore.product.current-rate": "<PERSON><PERSON> actual", "kgstore.product.error-max-amount": "La cantidad máxima es", "kgstore.product.error-max-quantity": "La cantidad máxima es", "kgstore.product.error-merchant": "No se pudo recuperar la información del comerciante.", "kgstore.product.error-min-amount": "La cantidad mínima es", "kgstore.product.error-min-quantity": "La cantidad mínima es", "kgstore.product.fee-desc-1": "El precio total incluye todas las tarifas. La cantidad de compra real se calculará de la siguiente manera", "kgstore.product.fee-desc-2": "Si la tarifa de tramitación de la cantidad de compra es inferior al mínimo, el precio total se ajustará en consecuencia.", "kgstore.product.fee-formula-1": "total/tipo de cambio.", "kgstore.product.fee-formula-2": "total/ (tipo de cambio* (1+comisión_proporcional).", "kgstore.product.handling-fee": "Tarifa de tramitación", "kgstore.product.include-fee": "El precio incluye {fee_percentage}un% de comisión de tramitación", "kgstore.product.introduction": "Introducción", "kgstore.product.limit": "Límite", "kgstore.product.market-profile": "Perfil de mercado", "kgstore.product.minimum-handling-fee": "Tarifa mínima de tramitación", "kgstore.product.no-handling-fee": "Sin gastos de tramitación", "kgstore.product.no-product": "El propietario no publicó ningún producto.", "kgstore.product.receive": "y recibe", "kgstore.product.spend": "<PERSON><PERSON><PERSON> gastar", "kgstore.product.title": "Productos", "kgstore.product.transfer-funds": "Transfiera fondos a", "kgstore.profile.title": "Perfil", "kgstore.toast.guest-desc": "¡Inicia sesión de inmediato y verifica tu identidad para asegurarte de que puedes hacer un pedido cada vez que veas un producto que te guste!", "kgstore.toast.guest-title": "Disfruta de la plataforma de intercambio de criptomonedas más segura", "kgstore.toast.pending-title": "Se está revisando su solicitud de identidad.", "kgstore.toast.rejected-desc": "Ponte en contacto con nuestro servicio de atención al cliente para obtener más información.", "kgstore.toast.rejected-title": "Se ha rechazado la solicitud de identidad.", "kgstore.toast.unverified-title": "Verifique su identidad para comenzar a operar.", "kgstore.toast.verified-desc": "¡Su solicitud de identidad ha sido verificada! Ya puede realizar un pedido.", "kgstore.toast.verified-title": "¡Identidad verificada correctamente!", "kgstore.unpaid.cta": "He realizado el pago", "kgstudio.asset.amount-invalid-description-1": "Ajusta la cantidad o ponte en contacto con el administrador para depositar al menos", "kgstudio.asset.amount-invalid-description-2": "y", "kgstudio.asset.amount-invalid-description-3": "para garantizar que se pueda realizar la transacción.", "kgstudio.asset.available-balance": "<PERSON>do disponible: ", "kgstudio.asset.balance": "Saldo de activos", "kgstudio.asset.balance-checking": "Verificación del saldo...", "kgstudio.asset.balance-value": "Equilibrio: {formattedTokenBalance}", "kgstudio.asset.checking-kya": "Comprobar el riesgo potencial de la dirección del monedero", "kgstudio.asset.checking-kyc": "Comprobar el estado de KYC", "kgstudio.asset.checklist.contact-hint": "Saldo restante hoy: 0$ (límite de transferencia diaria: 0$) Parece que el administrador o el propietario del sistema no han establecido tu límite de transferencia. Ponte en contacto con ellos para que te ayuden a configurar el límite de transferencia.", "kgstudio.asset.checklist.edit-hint": "Edita tu límite de transferencias", "kgstudio.asset.checklist.exceed-hint": "Si supera el importe que se puede transferir, reduzca el importe. (Límite de transferencia restante hoy: ${remainLimit})", "kgstudio.asset.checklist.remain-hint": "Saldo restante hoy: $ {remainLimit}(límite de transferencia diaria: ${dailyTransferLimit})", "kgstudio.asset.checklist.reuqired-approval": "Es necesario revisar esta transacción. (Umbral de aprobación: ${threshold})", "kgstudio.asset.checklist.reuqired-hint": "La cantidad debe ser mayor que 0. {remainLimit}(Saldo restante hoy: $)", "kgstudio.asset.check-tx": "Compruebe la transacción", "kgstudio.asset.customer-transfer-time": "Tiempo de notificación de pago", "kgstudio.asset.deposit-now": "De<PERSON><PERSON><PERSON> ahora", "kgstudio.asset.edit-now": "<PERSON><PERSON>", "kgstudio.asset.edit-order-modal.accepted-file-types": "Solo acepta tipos de archivos: png, jpg, jpeg, webp", "kgstudio.asset.edit-order-modal.cancel-transaction-instruction": "Si deseas detener la transacción, cancela este pedido.", "kgstudio.asset.edit-order-modal.change-status-to": "Cambiar estado a", "kgstudio.asset.edit-order-modal.edit-unpaid-himt": "Complete la siguiente información según el comprobante de pago proporcionado por el cliente", "kgstudio.asset.edit-order-modal.file-size-error": "El tamaño del archivo supera 10 MB", "kgstudio.asset.edit-order-modal.max-files-error": "Se ha superado el número máximo de archivos", "kgstudio.asset.edit-order-modal.max-file-upload-info": "Se pueden cargar hasta 10 archivos, el tamaño de cada archivo no supera 1 MB", "kgstudio.asset.edit-order-modal.payment-amount-mismatch": "El importe real del pago difiere del importe del pedido", "kgstudio.asset.edit-order-modal.payment-done-hint": "El cliente ha completado el pago; organice el envío con prontitud. Si no aceptas esta transacción, debes gestionar el reembolso inmediatamente después de cancelar el pedido.", "kgstudio.asset.edit-order-modal.payment-note": "Nota de pago", "kgstudio.asset.edit-order-modal.title": "Editar de<PERSON>les de pago", "kgstudio.asset.edit-order-modal.upload-attachments": "Cargar archivos adjuntos", "kgstudio.asset.edit-tx-note-modal.attachments.title": "<PERSON><PERSON><PERSON>", "kgstudio.asset.edit-tx-note-modal.note.hint": "Ingresa la nota de la transacción y carga los archivos adjuntos necesarios para su aprobación.", "kgstudio.asset.edit-tx-note-modal.note.placeholder": "Notas de transacción", "kgstudio.asset.edit-tx-note-modal.note.title": "Nota de transacción", "kgstudio.asset.edit-tx-note-modal.title": "<PERSON><PERSON>", "kgstudio.asset.edit-tx-note-modal.update-failed-error": "Marcador de posición de traducción", "kgstudio.asset.estimated-gas": "Tarifa de gas estimada:", "kgstudio.asset.estimated-gas-checking": "Tarifa de gas estimada: comprobando...", "kgstudio.asset.finance.loading": "Generando datos de panel...", "kgstudio.asset.gas-insufficient": "Saldo insuficiente de fichas de gas.", "kgstudio.asset.kya-info.total-balance": "Saldo de activos", "kgstudio.asset.kya-info.total-received": "Total recibido", "kgstudio.asset.kya-info.total-spent": "Gasto total", "kgstudio.asset.kya-info.total-transactions": "Total de transacciones", "kgstudio.asset.kya-status.atm.desc": "Operador de cajeros automáticos de criptomonedas.", "kgstudio.asset.kya-status.atm.name": "CAJERO AUTOMÁTICO", "kgstudio.asset.kya-status.child-exploitation.desc": "No hay información adicional.", "kgstudio.asset.kya-status.child-exploitation.name": "Explotación infantil", "kgstudio.asset.kya-status.dark-market.desc": "Un mercado en línea que opera a través de redes oscuras y se utiliza para intercambiar productos ilegales por criptomonedas.", "kgstudio.asset.kya-status.dark-market.name": "Darknet Marketplace", "kgstudio.asset.kya-status.dark-service.desc": "Una organización que opera a través de redes oscuras y ofrece servicios ilegales para criptomonedas.", "kgstudio.asset.kya-status.dark-service.name": "<PERSON><PERSON><PERSON>", "kgstudio.asset.kya-status.enforcement-action.desc": "La entidad está sujeta a procedimientos legales. Las jurisdicciones se anotarán como un subtipo.", "kgstudio.asset.kya-status.enforcement-action.name": "Acción de cumplimiento", "'kgstudio.asset.kya-status.error'": "En este momento no se puede realizar un análisis de riesgos para esta dirección, inténtelo de nuevo más tarde.", "kgstudio.asset.kya-status.error": "En este momento no se puede realizar un análisis de riesgos para esta dirección, inténtelo de nuevo más tarde.", "kgstudio.asset.kya-status.exchange-fraudulent.desc": "Un intercambio que estuvo involucrado en una actividad ilegal.", "kgstudio.asset.kya-status.exchange-fraudulent.name": "Intercambio fraudulento", "kgstudio.asset.kya-status.exchange-licensed.desc": "La entidad posee una licencia comercial específica para los criptoactivos, incluida la custodia, el intercambio, el corretaje o cualquier otro servicio financiero relacionado. Proporciona un servicio de intercambio en el que los participantes interactúan con una parte central (la entidad). No incluye las licencias de servicios financieros no específicas ni las jurisdicciones que figuran como no cooperadoras con el GAFI.", "kgstudio.asset.kya-status.exchange-licensed.name": "Intercambio: con licencia", "kgstudio.asset.kya-status.exchange-unlicensed.desc": "La entidad no posee ninguna licencia comercial específica para criptoactivos y proporciona un servicio de intercambio en el que los participantes interactúan con una parte central (la entidad). Incluye a las entidades autorizadas, pero en las jurisdicciones que figuran como no cooperadoras con el GAFI.", "kgstudio.asset.kya-status.exchange-unlicensed.name": "Intercambio: sin licencia", "kgstudio.asset.kya-status.gambling.desc": "Un recurso en línea que ofrece servicios de juegos de azar con criptomonedas.", "kgstudio.asset.kya-status.gambling.name": "Juegos de azar", "kgstudio.asset.kya-status.high": "Alto", "kgstudio.asset.kya-status.high-risk": "Alto riesgo", "kgstudio.asset.kya-status.illegal-service.desc": "Un recurso que ofrece servicios ilegales o participa en actividades ilegales.", "kgstudio.asset.kya-status.illegal-service.name": "<PERSON><PERSON><PERSON>", "kgstudio.asset.kya-status.liquidity-pools.desc": "No hay información adicional.", "kgstudio.asset.kya-status.liquidity-pools.name": "Fondos de liquidez", "kgstudio.asset.kya-status.low": "<PERSON><PERSON>", "kgstudio.asset.kya-status.low-risk": "<PERSON>jo riesgo", "kgstudio.asset.kya-status.marketplace.desc": "Una entidad que ofrece servicios legales/bienes comerciales para criptomonedas.", "kgstudio.asset.kya-status.marketplace.name": "Mercado en línea", "kgstudio.asset.kya-status.medium": "Mediano", "kgstudio.asset.kya-status.medium-risk": "Riesgo medio", "kgstudio.asset.kya-status.miner.desc": "Una organización que utiliza su potencia informática para extraer bloques de criptomonedas.", "kgstudio.asset.kya-status.miner.name": "<PERSON><PERSON>", "kgstudio.asset.kya-status.mixer.desc": "Un servicio para mezclar fondos de diferentes fuentes para que sea más difícil o casi imposible rastrearlos. Se usa principalmente para el lavado de dinero.", "kgstudio.asset.kya-status.mixer.name": "<PERSON><PERSON><PERSON>", "kgstudio.asset.kya-status.not-enough-info": "La dirección no tiene suficiente información para el análisis de riesgos.", "kgstudio.asset.kya-status.other.desc": "No hay información adicional.", "kgstudio.asset.kya-status.other.name": "Otras fuentes confiables", "kgstudio.asset.kya-status.others.desc": "No hay información adicional.", "kgstudio.asset.kya-status.others.name": "<PERSON><PERSON><PERSON>", "kgstudio.asset.kya-status.p2p-exchange-licensed.desc": "La entidad posee una licencia comercial específica para los criptoactivos, incluida la custodia, el intercambio, el corretaje o cualquier otro servicio financiero relacionado. Proporciona un servicio de intercambio en el que los participantes intercambian directamente entre sí. No incluye las licencias de servicios financieros no específicas ni las jurisdicciones que figuran como no cooperadoras con el GAFI.", "kgstudio.asset.kya-status.p2p-exchange-licensed.name": "Intercambio P2P: con licencia", "kgstudio.asset.kya-status.p2p-exchange-unlicensed.desc": "La entidad no posee ninguna licencia comercial específica para criptoactivos y proporciona un servicio de intercambio en el que los participantes intercambian directamente. Incluye a las entidades autorizadas, pero en las jurisdicciones que figuran como no cooperadoras con el GAFI.", "kgstudio.asset.kya-status.p2p-exchange-unlicensed.name": "Intercambio P2P: sin licencia", "kgstudio.asset.kya-status.payment.desc": "Un servicio que actúa como intermediario entre los clientes y la empresa que presta servicios para realizar un pago.", "kgstudio.asset.kya-status.payment.name": "Procesador de pagos", "kgstudio.asset.kya-status.potential-risk": "Riesgo potencial de dirección", "kgstudio.asset.kya-status.ransom.desc": "Extorsionadores que exigen el pago en forma de criptomonedas.", "kgstudio.asset.kya-status.ransom.name": "Extorsionador de rescates", "kgstudio.asset.kya-status.sanctions.desc": "No hay información adicional.", "kgstudio.asset.kya-status.sanctions.name": "Sanciones", "kgstudio.asset.kya-status.scam.desc": "Entidades que han estafado a sus clientes y se han apoderado de sus criptomonedas.", "kgstudio.asset.kya-status.scam.name": "Estafa", "kgstudio.asset.kya-status.seized-assets.desc": "No hay información adicional.", "kgstudio.asset.kya-status.seized-assets.name": "Activos incautados", "kgstudio.asset.kya-status.source.suspicious": "<PERSON><PERSON><PERSON>", "kgstudio.asset.kya-status.source.trusted": "Fu<PERSON>s confiables", "kgstudio.asset.kya-status.stolen-coins.desc": "Las entidades que se han apoderado de la criptomoneda de otra persona mediante hackeo.", "kgstudio.asset.kya-status.stolen-coins.name": "<PERSON><PERSON><PERSON> robadas", "kgstudio.asset.kya-status.terrorist-financing.desc": "No hay información adicional.", "kgstudio.asset.kya-status.terrorist-financing.name": "Financiamiento del terrorismo", "kgstudio.asset.kya-status.view-potential-risk-details": "Ver detalles de los posibles riesgos", "kgstudio.asset.kya-status.wallet-address-risk": "Riesgo de dirección de billetera", "kgstudio.asset.kya-status.wallet.desc": "Un servicio para almacenar y realizar pagos con criptomonedas.", "kgstudio.asset.kya-status.wallet.name": "Monedero en línea", "kgstudio.asset.kya-status.wallet-risk-checkin": "Analizando el riesgo de la cartera...", "kgstudio.asset.kyc-status.scan": "Escaneo de riesgos", "kgstudio.asset.kyc-status.scan-address": "Riesgo de dirección de billetera", "kgstudio.asset.kyc-status.subtitle": "Comprueba la información del usuario antes de realizar la transferencia.", "kgstudio.asset.market-profile.email": "Correo electrónico", "kgstudio.asset.market-profile.intro": "Introducción", "kgstudio.asset.market-profile.intro-tooltip": "Se muestra en la parte superior de la tienda.", "kgstudio.asset.market-profile.line-id": "IDENTIFICADOR DE LÍNEA", "kgstudio.asset.market-profile.logo": "Logotipo", "kgstudio.asset.market-profile.phone": "Teléfono", "kgstudio.asset.market-profile.section-title": "Perfil de mercado", "kgstudio.asset.market-profile.store-link": "Vista previa", "kgstudio.asset.market-profile.title": "<PERSON><PERSON><PERSON><PERSON>", "kgstudio.asset.market-profile.title-tooltip": "Se muestra en el título del sitio web.", "kgstudio.asset.market-profile.url": "URL", "kgstudio.asset.market-profile.url-tooltip": "Para personalizar tu dominio, ponte en contacto con el servicio de asistencia.", "kgstudio.asset.mismatch-error": "Este usuario no ha importado carteras de la cadena seleccionada ({selectedBlockchain}). Es posible que no vean el dinero que les envías.", "kgstudio.asset.note-attachments-required": "Ingresa la nota de la transacción y carga los archivos adjuntos necesarios para su aprobación. Si quieres enviar sin autorización, reduce el importe o modifica el límite.", "kgstudio.asset.order-detail.action-card.remain-balance": "<PERSON>do restante hoy: {balance}(Límite de transferencias diarias:{limit})", "kgstudio.asset.order-detail.action-card.tx-failed": "{txHash}Fallo en el envío de la transacción. Por favor, inténtelo de nuevo. ({shippedAt})", "kgstudio.asset.order-detail.actions.awaiting-confirmation": "El cliente nos ha notificado el pago; por favor, revísalo lo antes posible.", "kgstudio.asset.order-detail.actions.cancel-order": "Cancelar pedido", "kgstudio.asset.order-detail.actions.confirmation-hint": "Para evitar el lavado de dinero, verifica que la cuenta bancaria registrada del usuario coincida con la cuenta de remesa para asegurarte de que es su transacción. De lo contrario, puedes cancelar y reembolsar.", "kgstudio.asset.order-detail.actions.mark-as-paid": "Marcar como pagado", "kgstudio.asset.order-detail.actions.order-cancelled": "El pedido se ha cancelado.", "kgstudio.asset.order-detail.actions.order-done": "El pedido se ha completado.", "kgstudio.asset.order-detail.actions.order-shipping": "Actualmente se encuentra en tránsito. Comprueba el progreso del envío en tiempo real a través del comprobante de envío.", "kgstudio.asset.order-detail.actions.payment-deadline": "El cliente debe pagar antes; de {deadline}lo contrario, puedes cancelar este pedido.\n\nAntes de que el cliente realice el pago, si no aceptas la transacción, puedes cancelar el pedido sin procesar un reembolso.", "kgstudio.asset.order-detail.actions.title": "Acciones", "kgstudio.asset.order-detail.cancel-modal.cancel": "Cancelar este pedido", "kgstudio.asset.order-detail.cancel-modal.description": "Rellene la nota interna con el motivo. El usuario ha completado el pago. Debes procesar el reembolso lo antes posible después de cancelar este pedido.", "kgstudio.asset.order-detail.cancel-modal.title": "¿Estás seguro de que deseas cancelar este pedido?", "kgstudio.asset.order-detail.edit-internal-note": "Editar nota interna", "kgstudio.asset.order-detail.order-details": "Detalles del pedido", "kgstudio.asset.order-detail.order-information": "Información del pedido", "kgstudio.asset.order-detail.order-information.customer": "Cliente", "kgstudio.asset.order-detail.order-information.order-time": "Tiempo de pedido", "kgstudio.asset.order-detail.order-information.payment-details": "Detalles de pago", "kgstudio.asset.order-detail.order-information.product": "Producto", "kgstudio.asset.order-detail.order-information.qty": "Cantidad", "kgstudio.asset.order-detail.order-information.title": "Información del pedido", "kgstudio.asset.order-detail.order-information.total-price": "Precio total", "kgstudio.asset.order-detail.order-information.tx-id": "Número de pedido", "kgstudio.asset.order-detail.payment-details.account-info": "Nombre del banco: {bankName}\nNombre de la sucursal: {branchName}\nNúmero de cuenta: {accountNumber}\nNombre del titular de la cuenta: {accountHolderName}", "kgstudio.asset.order-detail.payment-details.account-info-less-content": "{bankName}{branchName}\nNúmero de cuenta: {accountNumber}\nNombre del titular de la cuenta: {accountHolderName}", "kgstudio.asset.order-detail.payment-details.confirmed-as-paid": "Se ha confirmado que este pedido ha sido pagado.", "kgstudio.asset.order-detail.payment-details.customer-account": "Información de la cuenta bancaria personal registrada del usuario (consulta los detalles de KYC del usuario para obtener más información):", "kgstudio.asset.order-detail.payment-details.info-from-customer": "Información de pago del cliente", "kgstudio.asset.order-detail.payment-details.last-five-digits": "Últimos cinco dígitos del número de cuenta de pago real", "kgstudio.asset.order-detail.payment-details.title": "Detalles de pago", "kgstudio.asset.order-detail.payment-details.unpaid": "El cliente aún no ha notificado el pago.", "kgstudio.asset.order-detail.payment-details.unpaid-time-reminder": "La fecha límite de pago es{deadline}, y aún quedan antes {timeLeft} de la fecha límite de pago.", "kgstudio.asset.order-detail.summary.internal-note": "Nota interna", "kgstudio.asset.order-detail.summary.internal-note-hint": "Complete detalles como el estado de procesamiento del pedido, los motivos de la cancelación del pedido, la información de reembolso y el progreso.", "kgstudio.asset.order-detail.summary.payment-status": "Estado del pago", "kgstudio.asset.order-detail.summary.process-by": "Procesado por", "kgstudio.asset.order-detail.summary.shipment-status": "Estado del envío", "kgstudio.asset.order-detail.summary.shipping-proof": "Comprobante de envío", "kgstudio.asset.order-detail.summary.title": "Resumen", "kgstudio.asset.order-settings.payment-terms": "Condiciones de pago", "kgstudio.asset.order-settings.payment-terms-tooltip": "Una vez establecido el pedido, se mostrará automáticamente un recordatorio de la fecha límite de pago. Aun así, tendrás que cancelar tú mismo el pedido caducado.", "kgstudio.asset.order-settings.section-title": "Configuración del pedido", "kgstudio.asset.orders.just now": "justo ahora", "kgstudio.asset.orders.just-now": "justo ahora", "kgstudio.asset.orders.list.customer": "Cliente", "kgstudio.asset.orders.list.order-created": "Pedido c<PERSON>o", "kgstudio.asset.orders.list.order-id": "ID de pedido", "kgstudio.asset.orders.list.order-purchase": "<PERSON><PERSON><PERSON><PERSON>", "kgstudio.asset.orders.list.order-status": "Estado del pedido", "kgstudio.asset.orders.list.payment": "Pago", "kgstudio.asset.orders.list.shipment": "envío", "kgstudio.asset.orders.list.total-price": "Precio total", "kgstudio.asset.orders.search.placeholder": "ID de pedido, cliente", "kgstudio.asset.orders.steps.awaiting-payment": "En espera de pago", "kgstudio.asset.orders.steps.awaiting-shipment": "En espera de envío", "kgstudio.asset.orders.steps.order-cancelled": "Pedido cancelado", "kgstudio.asset.orders.steps.order-completed": "Pedido completado", "kgstudio.asset.orders.steps.order-created": "Pedido c<PERSON>o", "kgstudio.asset.orders.steps.paid": "<PERSON><PERSON>", "kgstudio.asset.orders.steps.sent": "Enviado", "kgstudio.asset.orders.steps.shipping": "Envío", "kgstudio.asset.order-status.awaiting-confirmation": "En espera de confirmación", "kgstudio.asset.order-status.awaiting-shipment": "En espera de envío", "kgstudio.asset.order-status.cancelled": "Cancelado", "kgstudio.asset.order-status.delivered": "<PERSON><PERSON><PERSON>", "kgstudio.asset.order-status.shipping": "Envío", "kgstudio.asset.order-status.unpaid": "No remunerado", "kgstudio.asset.orders.title": "Pedidos", "kgstudio.asset.payment-info.account-holder-name": "Nombre del titular de la cuenta", "kgstudio.asset.payment-info.account-number": "Número de cuenta", "kgstudio.asset.payment-info.bank-name": "Nombre del banco", "kgstudio.asset.payment-info.bank-transfer": "Transferencia bancaria", "kgstudio.asset.payment-info.branch-name": "Nombre de sucursal", "kgstudio.asset.payment-info.currency": "Moneda de pago", "kgstudio.asset.payment-info.payment-method": "Método de pago", "kgstudio.asset.payment-info.section-title": "Información de pago", "kgstudio.asset.payment-status.awaiting-refund": "En espera de reembolso", "kgstudio.asset.payment-status.paid": "<PERSON><PERSON>", "kgstudio.asset.payment-status.refunded": "Reembolsado", "kgstudio.asset.payment-status.unpaid": "No remunerado", "kgstudio.asset.products.action": "Acción", "kgstudio.asset.products.available": "Disponible", "kgstudio.asset.products.cancel-modal-cancel": "<PERSON><PERSON>, cancelar", "kgstudio.asset.products.cancel-modal-hint": "¿Estás seguro de que quieres cancelar? Los datos que acabas de editar no se guardarán", "kgstudio.asset.products.cancel-modal-stay": "No, quédate", "kgstudio.asset.products.edit-product": "Editar producto", "kgstudio.asset.products.fee": "<PERSON><PERSON><PERSON>", "kgstudio.asset.products.fee-free-reminder-description": "Por ejemplo: Si gastas 10 000, {quoteCurrency}recibirás (10000/{currentPrice}= {quoteAmount}{baseCurrency}({chain})", "kgstudio.asset.products.fee-free-reminder-title": "Precio total = Precio × Cantidad del token", "kgstudio.asset.products.fee-included-reminder-description": "Por ejemplo: Si gastas 10 000, {quoteCurrency}recibirás （10000/{quoteDenominator}/{currentPrice})={quoteAmount}{baseCurrency}({chain})", "kgstudio.asset.products.fee-included-reminder-title": "Precio total = (1 + «Tarifa de tramitación») × Precio × Importe del token", "kgstudio.asset.products.fee-included-reminder-with-min-fee-description": "Por ejemplo: Si gastas 10 000, {quoteCurrency}recibirás (10000-{minFee})/{currentPrice}= {quoteAmount}{baseCurrency}({chain})", "kgstudio.asset.products.fee-included-reminder-with-min-fee-title": "Precio total = tarifa m<PERSON>+precio × cantidad del token", "kgstudio.asset.products.handling-fee": "Tarifa de tramitación", "kgstudio.asset.products.handling-fee-hint": "mínimo: 0%, máximo: 100%", "kgstudio.asset.products.handling-fee-no": "Sin gastos de tramitación", "kgstudio.asset.products.handling-fee-proportional": "Tarifa de tramitación (añadida al precio)", "kgstudio.asset.products.handling-fee-yes": "Tiene una tarifa de tramitación", "kgstudio.asset.products.image-size": "Tamaño: 200*200 px", "kgstudio.asset.products.inventory-greater-than-zero": "El inventario de la pantalla debe ser superior a cero.", "kgstudio.asset.products.inventory-less": "El inventario de pantalla debe ser inferior a *************.000.", "kgstudio.asset.products.limit-from-required": "Se requiere un límite de pedido", "kgstudio.asset.products.limit-to-required": "Se requiere un límite de pedido", "kgstudio.asset.products.limit-validation": "El rango de límite de pedido debe ser un número válido", "kgstudio.asset.products.minimum-fee": "<PERSON><PERSON><PERSON> m<PERSON>", "kgstudio.asset.products.name-required": "El nombre del producto es obligatorio", "kgstudio.asset.products.not-ready-for-publish-description": "Termine de editar la configuración del producto antes de publicarlo", "kgstudio.asset.products.not-ready-for-publish-title": "La información del producto no se ha establecido, por lo que no se puede publicar.", "kgstudio.asset.products.order-limit": "Límite de pedido", "kgstudio.asset.products.order-limits": "Límites de pedidos", "kgstudio.asset.products.price": "Precio", "kgstudio.asset.products.price-required": "El precio del producto es obligatorio", "kgstudio.asset.products.product-name": "Nombre del producto", "kgstudio.asset.products.product-type": "Tipo de producto", "kgstudio.asset.products.product-type-buy-crypto": "Comp<PERSON> crip<PERSON>", "kgstudio.asset.products.publish": "Publicar", "kgstudio.asset.products.reset-all": "Restaurar", "kgstudio.asset.products.reset-all-title": "Restaurar la información a la configuración anterior", "kgstudio.asset.products.reset-image": "Restablecer imagen", "kgstudio.asset.products.reset-to-default": "Restablecer a los valores predeterminados", "kgstudio.asset.products.status-published": "Publicado", "kgstudio.asset.products.status-unpublished": "Inédito", "kgstudio.asset.products.stock": "Inventario", "kgstudio.asset.products.stock-hint": "Inventario disponible en AssetPro Treasury: {tokenAmount}", "kgstudio.asset.products.trading-pair": "Par de negociación", "kgstudio.asset.products.updated": "Actualizada", "kgstudio.asset.products.update-failure-toast": "No se pudo actualizar «{baseCurrency}/{quoteCurrency}({chain})». Por favor, inténtelo de nuevo. (Código de error:{code})", "kgstudio.asset.products.update-success-toast": "«{baseCurrency}/{quoteCurrency}({chain})» se actualizó correctamente.", "kgstudio.asset.recipient-address": "Destinatario (dirección de la cartera)", "kgstudio.asset.recipient-send-by": "Destinatario (enviado por{sendType})", "kgstudio.asset.remain-limit-checking": "Límite de transferencia diaria restante: comprobando...", "kgstudio.asset.remain-limit-info": "Límite de transferencia diaria restante: $ {remainLimit}de ${dailyTransferLimit}.", "kgstudio.asset.remain-limit-info-invalid": "No puedes enviar ninguna transacción hoy ni ponerte en contacto con los administradores para ajustar tu límite de transferencia. ", "kgstudio.asset.remain-limit-invalid-hint": "Ajusta el importe o ponte en contacto con los administradores para ajustar el límite de transferencia. ", "kgstudio.asset.threshold-info": "Esta transacción requiere aprobación (umbral de aprobación: inferior a ${transferApprovalThreshold}).", "kgstudio.asset.token-and-gas-insufficient": "Balance insuficiente de fichas y gas.", "kgstudio.asset.token-insufficient": "<PERSON>do de fichas insuficiente.", "kgstudio.asset.transfer-amount": "Monto de la transferencia", "kgstudio.asset.transfer-to": "Transferir a", "kgstudio.asset.transfer-validation": "Solucione los problemas antes de enviar:", "kgstudio.asset.transfer-validation-amount-invalid": "El importe del token de transferencia no es válido.", "kgstudio.asset.transfer-validation-info-invalid": "La nota de transacción y el adjunto son necesarios para la aprobación", "kgstudio.asset.transfer-validation-recipient-invalid": "El destinatario es obligatorio. Haz clic en «Análisis de riesgos» antes de realizar cualquier transacción.", "kgstudio.asset.tx-action-card.approve": "<PERSON><PERSON><PERSON>", "kgstudio.asset.tx-action-card.awaiting-approval.msg-approver": "Revisa los detalles de la transacción y toma una decisión: aprueba o rechaza la transacción.", "kgstudio.asset.tx-action-card.awaiting-approval.msg-normal": "Esta transacción está en espera de aprobación. Si quieres acelerar el proceso, considera ponerte en contacto con los aprobadores.", "kgstudio.asset.tx-action-card.awaiting-release.msg-finance-manager": "Tenga en cuenta los detalles de la transacción y proceda a liberar la transacción para su ejecución o a rechazarla en consecuencia.", "kgstudio.asset.tx-action-card.awaiting-release.msg-normal": "Esta transacción está en espera de publicación. Si quieres acelerar el proceso, considera la posibilidad de ponerte en contacto con los gestores financieros.", "kgstudio.asset.tx-action-card.reject": "<PERSON><PERSON><PERSON>", "kgstudio.asset.tx-action-card.rejected.review-note.title": "Nota de revisión", "kgstudio.asset.tx-action-card.rejected.title": "Esta transacción ha sido rechazada ❌", "kgstudio.asset.tx-action-card.release": "Liberar", "kgstudio.asset.tx-action-card.release-check-list.balance-enough": "Saldo disponible: suficiente ({tokenBalance} {tokenSymbol})", "kgstudio.asset.tx-action-card.release-check-list.balance-loading": "Comprobar el saldo restante de la organización", "kgstudio.asset.tx-action-card.release-check-list.balance-not-enough.desc": "Recarga al menos {shortfallAmount}{tokenSymbol}({chainName}) antes de que el aprobador apruebe esta transacción.", "kgstudio.asset.tx-action-card.release-check-list.balance-not-enough.title": "Saldo disponible: insuficiente ({tokenBalance} {tokenSymbol})", "kgstudio.asset.tx-action-card.release-check-list.fee-enough": "Tarifa estimada de transferencia de gas: {fee}{tokenSymbol}", "kgstudio.asset.tx-action-card.release-check-list.fee-loading": "Comprobar el token nativo restante de la organización", "kgstudio.asset.tx-action-card.release-check-list.fee-not-enough.desc": "Recarga al menos antes de que el aprobador apruebe esta transacción {shortfallAmount} {tokenSymbol}({chainName}).", "kgstudio.asset.tx-action-card.release-check-list.fee-not-enough.title": "<PERSON>do de fichas nativas disponible: insuficiente. ", "kgstudio.asset.tx-action-card.send-failed.msg": "Para obtener más información, haz clic en el botón de abajo para ser redirigido al sitio web del explorador de cadenas de bloques.", "kgstudio.asset.tx-action-card.send-failed.title": "Transacción fallida ❌", "kgstudio.asset.tx-action-card.sending.msg": "Esta transacción se ha publicado y se está enviando. Ten en cuenta que puede tardar más de lo esperado si la cadena de bloques está congestionada.\n\nPara obtener más información, haz clic en el botón de abajo para ser redirigido al sitio web del explorador de cadenas de bloques.", "kgstudio.asset.tx-action-card.send-success.msg": "Para obtener más información, haz clic en el botón de abajo para ser redirigido al sitio web del explorador de cadenas de bloques.", "kgstudio.asset.tx-action-card.send-success.title": "Esta transacción se ha completado ✅", "kgstudio.asset.tx-action-card.title": "Acción", "kgstudio.asset.tx-approval-modal.insufficient-balance-error.desc": "Vuelva a intentarlo después de que el administrador haya depositado lo suficiente {shortfallAmount} {tokenSymbol} ({chainName})", "kgstudio.asset.tx-approval-modal.insufficient-balance-error.title": "Error: {tokenSymbol}{chainName}inventario insuficiente ()", "kgstudio.asset.tx-approval-modal.success.title": "<PERSON><PERSON><PERSON> satisfactoriam<PERSON>", "kgstudio.asset.tx-confirm-approval-modal.approve": "Confirme la aprobación", "kgstudio.asset.tx-confirm-approval-modal.insufficient-balance-error.description": "Vuelva a intentarlo después de que el administrador haya depositado suficientes ${amount} $ {tokenSymbol}(${chainName})", "kgstudio.asset.tx-confirm-approval-modal.insufficient-balance-error.title": "Error: {tokenSymbol}{chainName}inventario de $ ($) insuficiente", "kgstudio.asset.tx-confirm-approval-modal.send-token": "Enviar token", "kgstudio.asset.tx-confirm-approval-modal.title": "Confirme la aprobación", "kgstudio.asset.tx-confirm-approval-modal.warning.description": "Asegúrese de revisar cuidadosamente los detalles de la transacción y de verificar su legitimidad.", "kgstudio.asset.tx-confirm-approval-modal.warning.title": "¿Está seguro de que desea marcar esta transacción como aprobada?", "kgstudio.asset.tx-confirm-release-modal.release": "Confirmar publicación", "kgstudio.asset.tx-confirm-release-modal.warning.description": "Asegúrese de revisar cuidadosamente los detalles de la transacción y de verificar su legitimidad.", "kgstudio.asset.tx-confirm-release-modal.warning.title": "¿Estás seguro de que quieres liberar esta transacción?", "kgstudio.asset.tx-conform-release-modal.title": "Confirmar publicación", "kgstudio.asset.tx-detail.not-exist": "Este pedido no existe en esta organización ({orgName}). En su lugar, comprueba la organización correspondiente.", "kgstudio.asset.tx-detail.steps.awaiting-approval": "En espera de aprobación", "kgstudio.asset.tx-detail.steps.awaiting-release": "En espera de publicación", "kgstudio.asset.tx-detail.steps.rejected": "<PERSON><PERSON><PERSON><PERSON>", "kgstudio.asset.tx-detail.steps.send-failed": "Error en el envío", "kgstudio.asset.tx-detail.steps.sending": "Enviando", "kgstudio.asset.tx-detail.steps.send-success": "Enviar con éxito", "kgstudio.asset.tx-detail.steps.submitted": "Enviado", "kgstudio.asset.tx-detail.tx-info-card.tx-id": "ID de transacción", "kgstudio.asset.tx-error-generic": "Se ha producido un error", "kgstudio.asset.tx-error-processing": "Se ha producido un error al procesar la solicitud. Vuelva a intentarlo más tarde.", "kgstudio.asset.tx-failed": "Falló el envío de la transacción. Vuelva a intentarlo. ", "kgstudio.asset.tx-history.approved": "{name}ap<PERSON>bado", "kgstudio.asset.tx-history.approved-by": "Aprobado por", "kgstudio.asset.tx-history-card.approver": "Aprobador", "kgstudio.asset.tx-history-card.finance-manager": "Gerente de finanzas", "kgstudio.asset.tx-history-card.submitted-by": "Presentado por", "kgstudio.asset.tx-history-card.title": "Historia", "kgstudio.asset.tx-history-card.tx-hashes": "Hash de transacción", "kgstudio.asset.tx-history.latest-update": "Última actualización", "kgstudio.asset.tx-history.rejected": "{name}re<PERSON><PERSON><PERSON>", "kgstudio.asset.tx-history.rejected-by": "Rechazado por", "kgstudio.asset.tx-history.released": "{name}la<PERSON><PERSON>", "kgstudio.asset.tx-history.released-by": "Publicado por", "kgstudio.asset.tx-history.submitted": "{name}presentado", "kgstudio.asset.tx-info-card.attachments": "<PERSON><PERSON><PERSON>", "kgstudio.asset.tx-info-card.blockchain": "Cadena de bloques", "kgstudio.asset.tx-info-card.recipient": "Receptor", "kgstudio.asset.tx-info-card.send-token": "Enviar token", "kgstudio.asset.tx-info-card.title": "Información sobre transacciones", "kgstudio.asset.tx-info-card.tx-note": "Nota de transacción", "kgstudio.asset.tx-info-card.tx-status": "Estado de la transacción", "kgstudio.asset.tx-insufficient-balance": "El equilibrio no es suficiente", "kgstudio.asset.tx-insufficient-balance-admin-recharge": "Haga que el administrador deposite al menos {insufficientAmount}{tokenName}({chainName}) en la Tesorería y vuelva a intentarlo", "kgstudio.asset.tx-limit-exceeded": "Se ha superado el límite de transferencia", "kgstudio.asset.tx-limit-exceeded-contact-admin": "Se ha superado el límite de transferencia; póngase en contacto con el administrador para obtener ayuda", "kgstudio.asset.tx-need-approval": "Esta transacción se enviará solo después de la aprobación.", "kgstudio.asset.tx-need-approval-hint": "Asegúrese de que los detalles de la transacción sean correctos, ya que no se pueden modificar después del envío.", "kgstudio.asset.tx-rejection-modal.confirm-rejection": "Confirme el rechazo", "kgstudio.asset.tx-rejection-modal.review-note.hint": "Motivo del rechazo", "kgstudio.asset.tx-rejection-modal.review-note.required-error": "Indique el motivo del rechazo", "kgstudio.asset.tx-rejection-modal.review-note.title": "Nota de revisión", "kgstudio.asset.tx-rejection-modal.success.title": "Re<PERSON>zado correctamente", "kgstudio.asset.tx-rejection-modal.title": "Confirme el rechazo", "kgstudio.asset.tx-rejection-modal.warning.desc": "Asegúrate de revisar cuidadosamente los detalles de la transacción y de indicar el motivo del rechazo.", "kgstudio.asset.tx-rejection-modal.warning.title": "¿Estás seguro de que deseas rechazar esta transacción?", "kgstudio.asset.understand": "Entiendo", "kgstudio.asset.wallet-risk-check": "Verificación de riesgo de billetera", "kgstudio.audience.compliance": "Cumplimiento", "kgstudio.audience.country": "<PERSON><PERSON>", "kgstudio.audience.email": "Correo electrónico", "kgstudio.audience.kyc_status": "KYC", "kgstudio.audience.name": "Nombre", "kgstudio.audience.nft_projects": "Proyectos NFT", "kgstudio.audience.phone": "Teléfono", "kgstudio.audience.query-placeholder": "Nombre, correo electrónico o teléfono", "kgstudio.audience.wallet_id": "ID de cartera: ", "kgstudio.auth.login.accept-crypto-for": "Acepta criptomonedas para", "kgstudio.auth.login.continue-with-email": "Continuar con el Email", "kgstudio.auth.login.module.integration-options.description": "Ofrece soluciones sin código, integración con SDK y consultas de API prácticas para diversas necesidades técnicas.", "kgstudio.auth.login.module.integration-options.title": "Integración flexible (sin código, SDK, API)", "kgstudio.auth.login.module.low-fees.description": "<PERSON>ri<PERSON>s mínimas que permiten a los creadores quedarse con una mayor parte de sus ganancias.", "kgstudio.auth.login.module.low-fees.title": "<PERSON><PERSON><PERSON><PERSON> bajas", "kgstudio.auth.login.module.no-kyc.description": "No se requiere verificación KYC ni registro comercial. Empieza a vender de inmediato.", "kgstudio.auth.login.module.no-kyc.title": "Sin restricciones", "kgstudio.auth.login.module.payment-options.description": "Admite recargas con tarjetas de crédito o pagos directos con monedero para satisfacer diversas necesidades de transacción.", "kgstudio.auth.login.module.payment-options.title": "Múltiples opciones de pago", "kgstudio.auth.login.rotator.artwork": "🎨 obra de arte", "kgstudio.auth.login.rotator.business": "💼 negocios", "kgstudio.auth.login.rotator.community": "👥 comunidad", "kgstudio.auth.login.rotator.content": "📝 creación de contenido", "kgstudio.auth.login.rotator.digital-goods": "🎁 productos digitales", "kgstudio.auth.login.rotator.website": "💻 sitio web", "kgstudio.check.change_acc_apologize": "Lo siento, no puedes unirte a este equipo.", "kgstudio.check.change_acc_desc": "Tu correo electrónico de inicio de sesión actual es {currentEmail}, por favor utiliza el correo electrónico de la invitación en su lugar: {inviteEmail} para iniciar sesión.", "kgstudio.check.change_acc_link": "<PERSON><PERSON><PERSON>", "kgstudio.check.change_acc_title": "Por favor, cambia tu cuenta de inicio de sesión para unirte al equipo.", "kgstudio.check.change_acc_toast": "Se cerró la sesión de la cuenta corriente. Se te redirigirá a la nueva cuenta.", "kgstudio.check.invalid_desc": "Por favor, verifica la URL o contacta al administrador de tu organización.", "kgstudio.check.invalid_link": "Volver a la página de inicio", "kgstudio.check.invalid_title": "Lo siento, este enlace ha expirado o la página no se puede encontrar", "kgstudio.check.invitation-accepted-cta": "In<PERSON><PERSON>", "kgstudio.check.invitation-accepted-desc": "Inicie sesión y comience su viaje", "kgstudio.check.invitation-accepted-title": "Enhorabuena. Te has registrado correctamente.", "kgstudio.check.loading_desc": "Estamos verificando la autorización...", "kgstudio.check.loading_title": "Por favor espera y no abandones o cierres esta página.", "kgstudio.common.accept": "Aceptar", "kgstudio.common.account_setting": "Configuración de la cuenta", "kgstudio.common.add-fund": "<PERSON><PERSON><PERSON>", "kgstudio.common.address": "Dirección", "kgstudio.common.address-copied": "Dirección copiada al portapapeles", "kgstudio.common.all-tasks": "Detalles de la tarea", "kgstudio.common.app-notification": "Notificación de aplicaciones", "kgstudio.common.app-publish": "Publicación de aplicaciones", "kgstudio.common.approved": "Aprobado", "kgstudio.common.asset": "AssetPro", "kgstudio.common.assets": "Activos", "kgstudio.common.attachments": "<PERSON><PERSON><PERSON>", "kgstudio.common.back": "Atrás", "kgstudio.common.back-to-login": "Volver al inicio de sesión", "kgstudio.common.billing": "Facturación", "kgstudio.common.blockchain": "Cadena de bloques", "kgstudio.common.cancel": "<PERSON><PERSON><PERSON>", "kgstudio.common.case-management": "Gestión de casos", "kgstudio.common.cdd-tasks": "<PERSON><PERSON>s de CDD", "kgstudio.common.change": "Cambiar", "kgstudio.common.clear-filter": "<PERSON><PERSON><PERSON> filtro", "kgstudio.common.close": "<PERSON><PERSON><PERSON>", "kgstudio.common.community-links": "Enlaces comunitarios", "kgstudio.common.complete": "Listo", "kgstudio.common.compliance": "Compliance", "kgstudio.common.configuration": "Configuración del monedero", "kgstudio.common.confirm-publish": "Confirmar publicación", "kgstudio.common.create": "<PERSON><PERSON><PERSON>", "kgstudio.common.create-a-task": "<PERSON><PERSON>r una tarea", "kgstudio.common.created": "Creado en", "kgstudio.common.created_at": "Creado en", "kgstudio.common.dapp-list": "Lista de dApp", "kgstudio.common.data.analysis": "Datos", "kgstudio.common.data.asset-pro": "AssetPro", "kgstudio.common.data.compliance": "Compliance", "kgstudio.common.data.nft-boost": "Actividad de NFT", "kgstudio.common.data.wallet": "Bill<PERSON>a", "kgstudio.common.description": "Descripción", "kgstudio.common.discord": "Discord", "kgstudio.common.edit": "<PERSON><PERSON>", "kgstudio.common.editor": "Redactor", "kgstudio.common.engage": "Herramienta de marketing (coming soon)", "kgstudio.common.error": "Algo salió mal. Vuelve a intentarlo más tarde o ponte en contacto con nuestro equipo de soporte para obtener ayuda.", "kgstudio.common.expired": "<PERSON><PERSON><PERSON>", "kgstudio.common.explorer-banner": "Banner Explorer", "kgstudio.common.export-private-key": "Exportar clave privada", "kgstudio.common.finance": "Finanzas", "kgstudio.common.general": "General", "kgstudio.common.get-started": "Comenzar", "kgstudio.common.idv-tasks": "Tareas de IDV", "kgstudio.common.image": "Imagen", "kgstudio.common.in-app-message": "Mensaje integrado en la aplicación", "kgstudio.common.insufficient_not_refunded": "Insuficiente (no reembolsado)", "kgstudio.common.insufficient_refunded": "Insuficiente (reembolsado)", "kgstudio.common.invoice-pro": "Factura (PRO)", "kgstudio.common.kyc-form": "Formulario KYC", "kgstudio.common.kyt-tasks": "<PERSON><PERSON>s K<PERSON>", "kgstudio.common.language.english": "English", "kgstudio.common.language.japanese": "日本語", "kgstudio.common.languages": "Idiomas", "kgstudio.common.language.simplified-chinese": "中文（简体）", "kgstudio.common.language.spanish": "Español", "kgstudio.common.language.traditional-chinese": "中文（繁體）", "kgstudio.common.language.vietnamese": "Tiếng <PERSON>", "kgstudio.common.last-edited-time": "Hora de la última edición", "kgstudio.common.line-id": "LINE ID", "kgstudio.common.liquidity": "Liquidez", "kgstudio.common.logout": "<PERSON><PERSON><PERSON>", "kgstudio.common.marketing-tools": "Herramientas de marketing", "kgstudio.common.market-settings": "Configuración del mercado", "kgstudio.common.maxInputHint": "1000", "kgstudio.common.member-id": "ID de miembro", "kgstudio.common.members": "Miembros del equipo", "kgstudio.common.minimum": "<PERSON><PERSON><PERSON>", "kgstudio.common.my-role": "mis roles\n", "kgstudio.common.my-shop": "<PERSON> tienda", "kgstudio.common.next": "Siguient<PERSON>", "kgstudio.common.next-step": "Próximo paso", "kgstudio.common.nft-airdrop": "Lanzamiento aéreo NFT", "kgstudio.common.nft-boost": "Impulso de NFT", "kgstudio.common.note-and-attachments": "Nota y archivos adjuntos", "kgstudio.common.notification": "Notificación", "kgstudio.common.operators": "Operadores", "kgstudio.common.optional": "Opcional", "kgstudio.common.orders": "Pedidos", "kgstudio.common.organization-id-required": "Se requiere el identificador de la organización", "kgstudio.common.overview": "Visión general", "kgstudio.common.page-desc": "Mostrar filas por página", "kgstudio.common.payment-address": "Dirección de pago", "kgstudio.common.pending": "Pendiente", "kgstudio.common.prev-step": "Paso anterior", "kgstudio.common.processing": "Cargando...", "kgstudio.common.products": "Producto", "kgstudio.common.profile": "Perfil", "kgstudio.common.project": "Proyecto", "kgstudio.common.project-updated": "¡Información del proyecto actualizada!", "kgstudio.common.push-notification": "Notificación push", "kgstudio.common.recaptcha-error": "No se pudo verificar Google reCAPTCHA. Actualiza la página web o ponte en contacto con nuestro equipo de asistencia para obtener ayuda.", "kgstudio.common.recipient": "Receptor", "kgstudio.common.reject": "<PERSON><PERSON><PERSON>", "kgstudio.common.rejected": "<PERSON><PERSON><PERSON><PERSON>", "kgstudio.common.reset": "Reiniciar", "kgstudio.common.revenue": "Finanzas", "kgstudio.common.revert": "<PERSON><PERSON><PERSON> y <PERSON>", "kgstudio.common.review": "Centro de revisión", "kgstudio.common.roles": "Funciones", "kgstudio.common.save": "Guardar", "kgstudio.common.save-changes": "¡Cambios guardados!", "kgstudio.common.save-draft": "Guardar borrador", "kgstudio.common.search": "Buscar", "kgstudio.common.send": "Enviar", "kgstudio.common.send-now": "<PERSON><PERSON><PERSON> ahora", "kgstudio.common.send-to": "Enviar a", "kgstudio.common.send-token": "Enviar token", "kgstudio.common.settings": "<PERSON><PERSON><PERSON><PERSON>", "kgstudio.common.start": "Empezar", "kgstudio.common.submit": "Enviar", "kgstudio.common.submit-request": "Enviar", "kgstudio.common.success": "Éxito", "kgstudio.common.successfully-copied": "¡Se copió correctamente!", "kgstudio.common.supported-chains": "Cadenas de bloques compatibles", "kgstudio.common.system-settings": "Configuración del sistema", "kgstudio.common.telegram": "Telegram", "kgstudio.common.top-up": "Recargar", "kgstudio.common.total": "Total", "kgstudio.common.transaction-status": "Estado de la transacción", "kgstudio.common.transfer": "Traslado", "kgstudio.common.treasury": "Tesoro", "kgstudio.common.twitter": "Twitter", "kgstudio.common.tx-hash": "Hash de transacción", "kgstudio.common.type": "Tipo", "kgstudio.common.type-some-description": "Escribe aquí una descripción.", "kgstudio.common.unset": "puesta de sol", "kgstudio.common.update": "Actualización", "kgstudio.common.updated_at": "Actualizado en", "kgstudio.common.update-time": "Hora de actualización", "kgstudio.common.uploading": "Subiendo...", "kgstudio.common.user": "Configuración de usuario", "kgstudio.common.user360": "User 360", "kgstudio.common.user-management": "Todos los usuarios", "kgstudio.common.user-not-found": "No se encontró el usuario de Studio. Póngase en contacto con el administrador de su organización.", "kgstudio.common.users": "Usuarios", "kgstudio.common.wallet": "<PERSON><PERSON><PERSON> de cartera<PERSON>", "kgstudio.compliance.cdd-tasks": "<PERSON><PERSON>s de CDD", "kgstudio.compliance.idv-tasks": "Tareas de IDV", "kgstudio.compliance.kyt-tasks": "<PERSON><PERSON>s K<PERSON>", "kgstudio.compliance.title": "Cumplimiento", "kgstudio.data.actions-section": "Acciones", "kgstudio.data.active-users": "Usuarios activos", "kgstudio.data.ai-generate": "¡Generar IA!", "kgstudio.data.ai-recommend": "Recomendación de IA", "kgstudio.data.all": "Todos", "kgstudio.data.app-store-info-language": "Idioma de información de la App Store", "kgstudio.data.asset-pro.date.all": "<PERSON><PERSON>", "kgstudio.data.asset-pro.date.last-14-days": "Últimos 14 días", "kgstudio.data.asset-pro.date.last-30-days": "Últimos 30 días", "kgstudio.data.asset-pro.date.last-7-days": "Últimos 7 días", "kgstudio.data.asset-pro.date.title": "<PERSON><PERSON>", "kgstudio.data.asset-pro.error": "No se pudieron generar los datos, inténtelo de nuevo más tarde.", "kgstudio.data.asset-pro.loading": "Generando un panel de control para los datos de AssetPro...", "kgstudio.data.asset-pro.retry": "Reintentar", "kgstudio.data.balance": "Equilibrio", "kgstudio.data.balance-greater-than-zero": "Equilibrio > 0", "kgstudio.data.budget-title": "Presupuesto", "kgstudio.data.choose-plan": "Elige un plan", "kgstudio.data.churn-rate": "<PERSON><PERSON>", "kgstudio.data.churn-users": "Usuarios de Churn", "kgstudio.data.compliance.cdd-tasks": "Número de revisiones de casos", "kgstudio.data.compliance.form-submission": "Envío de formularios", "kgstudio.data.compliance.idv-tasks": "Número de verificación de identidad", "kgstudio.data.compliance.kyc-status.title": "Verificación KYC", "kgstudio.data.compliance.no-data": "Aún no hay datos disponibles para el análisis", "kgstudio.data.compliance.pending-review": "Revisión pendiente", "kgstudio.data.compliance.personal-info.title": "Información personal", "kgstudio.data.compliance.verified-customers": "Clientes verificados", "kgstudio.data.compliance.v-wallet-usage.title": "Activación de monederos de clientes verificados", "kgstudio.data.compliance.v-wallet-usage.tooltip": "Si los usuarios verificados por KYC han activado (descargando e iniciando sesión) su billetera KryptoGo o no.", "kgstudio.data.create_nft_collection": "Crea una colección de NFT", "kgstudio.data.discard": "Descar<PERSON>", "kgstudio.data.engage.compliance": "Cumplimiento", "kgstudio.data.engage.country": "<PERSON><PERSON>", "kgstudio.data.engage-create": "Genere compromiso", "kgstudio.data.engage.email": "Correo electrónico", "kgstudio.data.engage.kyc": "KYC", "kgstudio.data.engage-list": "Lista de proyectos de participación", "kgstudio.data.engage.name": "Nombre", "kgstudio.data.engage.nft-projects": "Proyectos NFT", "kgstudio.data.engage.phone": "Teléfono", "kgstudio.data.engage-title": "Participar", "kgstudio.data.engage.wallet-id": "ID de cartera", "kgstudio.data.estimated-gas-fee": "Tarifa de gas estimada", "kgstudio.data.estimated-reach": "Alcance estimado", "kgstudio.data.events": "Eventos", "kgstudio.data.evm-wallet": "Billetera EVM", "kgstudio.data.file_types_supported": "Imágenes compatibles: JPG, PNG, GIF, SVG. Tamaño máximo: 10 MB (cambiar el nombre del formato de archivo no funcionará. Utilice los servicios de conversión de archivos si no tiene formatos de imagen compatibles).", "kgstudio.data.header": "Datos del monedero", "kgstudio.data.increase-user-retention": "Aumente la retención de usuarios", "kgstudio.data.increase-wallet-user-activity": "Activación de usuarios registrados", "kgstudio.data.increase-wallet-user-retention": "Retención de usuarios de Wallet", "kgstudio.data.invalid": "Confirme si la información se ha introducido correctamente", "kgstudio.data.login": "In<PERSON><PERSON>", "kgstudio.data.new-users": "Nuevos usuarios", "kgstudio.data.nft_opensea_banner_title": "OpenSea Banner", "kgstudio.data.notification.1-button": "1 botón", "kgstudio.data.notification.2-buttons": "2 botones", "kgstudio.data.notification.body": "<PERSON><PERSON><PERSON>", "kgstudio.data.notification.buttons": "Botón (s)", "kgstudio.data.notification.enter-title": "Introduce el título", "kgstudio.data.notification.file-types-supported": "Imágenes compatibles: JPG, PNG, GIF, SVG. Tamaño máximo: 10 MB (cambiar el nombre del formato de archivo no funcionará. Utilice los servicios de conversión de archivos si no tiene formatos de imagen compatibles).", "kgstudio.data.notification.first-button": "El primer botón", "kgstudio.data.notification.image": "Imagen", "kgstudio.data.notification.no-button": "Sin botón", "kgstudio.data.notification.recommended-size": "Tamaño recomendado: 1400 x 350 px", "kgstudio.data.notification.text-placeholder": "Texto", "kgstudio.data.recommended_size": "Tamaño recomendado: 600 x 200 px", "kgstudio.data.recommended-target": "Objetivo recomendado", "kgstudio.data.registered-in-7D": "Registrado en 7-Day", "kgstudio.data.registered-users": "Usuarios registrados", "kgstudio.data.retention": "Retención", "kgstudio.data.retention-rate": "Tasa de retención", "kgstudio.data.select_from_nft_boost": "Seleccione entre NFT Boost", "kgstudio.data.select-multiple-actions": "Puede configurar más de un tipo de activo o información para sus usuarios.", "kgstudio.data.send-currency": "<PERSON><PERSON><PERSON> moneda", "kgstudio.data.send-nft": "Enviar NFT", "kgstudio.data.send-notification": "Enviar notificación", "kgstudio.data.shop-info-language": "Versión en idioma de información de la tienda", "kgstudio.data.since-last-month": "Desde el mes pasado", "kgstudio.data.specify-engage-time": "Programar", "kgstudio.data.target-placeholder": "Su objetivo, por ejemplo, aumentar la retención", "kgstudio.data.target-section": "Objetivo", "kgstudio.data.target-title": "Gol", "kgstudio.data.time-option-custom": "Personalizado", "kgstudio.data.time-option-immediately": "Inmediatamente", "kgstudio.data.time-section": "<PERSON><PERSON>", "kgstudio.data.top-users": "Usuarios principales", "kgstudio.data.total-balance": "Saldo total", "kgstudio.data.tron-wallet": "Billetera Tron", "kgstudio.data.try-more": "Prueba más", "kgstudio.data.tx-events": "Eventos de transacciones", "kgstudio.data.upload-branding-assets": "Cargar activos de marca", "kgstudio.data.upload-csv-list": "Cargar lista en formato.csv", "kgstudio.data.use-assetpro": "Usa AssetPro para enviar criptomonedas a tus usuarios objetivo", "kgstudio.data.use-nft-boost": "Seleccione entre los artículos NFT existentes de NFT Boost", "kgstudio.data.user": "Usuario", "kgstudio.data.user-activities": "Actividades de los usuarios", "kgstudio.data.users-balance-greater": "Saldo de usuarios>0", "kgstudio.data.use-wallet-notification": "Envía notificaciones y anuncios a los usuarios de tu monedero", "kgstudio.data.wallet": "Bill<PERSON>a", "kgstudio.data.wallet-address": "Dirección del monedero", "kgstudio.data.wallet-balance-greater": "Saldo de cartera > 0", "kgstudio.data.wallets": "carteras", "kgstudio.data.your-prompt": "<PERSON> mensaje", "kgstudio.dna.create-at": "Creado en", "kgstudio.dna.token-holdings": "Tenencias de fichas", "kgstudio.engagement.actions-title": "Acciones", "kgstudio.engagement.activity": "Actividad", "kgstudio.engagement.all": "Todos", "kgstudio.engagement.asset-balance": "Saldo de activos", "kgstudio.engagement.behavior": "Comportamiento", "kgstudio.engagement.engaged": "Comprometidos", "kgstudio.engagement.methods": "<PERSON><PERSON><PERSON><PERSON>", "kgstudio.engagement.name": "Nombre del proyecto", "kgstudio.engagement.nft-claim-rate": "Tasa de reclamaciones de NFT", "kgstudio.engagement.notification-visit": "Visita de notificación", "kgstudio.engagement.rewards-redeem-rate": "Tasa de canje de recompensas", "kgstudio.engagement.send-currency": "<PERSON><PERSON><PERSON> moneda", "kgstudio.engagement.send-nft": "Enviar NFT", "kgstudio.engagement.send-notification": "Enviar notificación", "kgstudio.engagement.target-settings": "Configuración de objetivos", "kgstudio.engagement.time": "<PERSON><PERSON>", "kgstudio.engagement.title": "Compromiso", "kgstudio.engagement.users": "Usuarios", "kgstudio.error.cant-find-customer": "Lo sentimo<PERSON>, este usuario aún no se ha registrado en tu servicio. Si deseas transferir fondos a este usuario, pídele que primero se registre e inicie sesión en tu formulario KYC o en tu monedero.", "kgstudio.error.cant-find-user": "Lo siento, no podemos encontrar a este usuario.", "kgstudio.error.general-error": "Ha ocurrido un error. Código: ", "kgstudio.error.insufficient-balance": "<PERSON><PERSON> insuficiente. Añada más fondos al fondo de tesorería de su organización.", "kgstudio.error.no-access": "No tienes acceso. Utiliza una dirección de monedero verificada para iniciar sesión.", "kgstudio.error.no-organization": "<PERSON> sentimo<PERSON>, aún no te has unido a ninguna organización", "kgstudio.error.no-organization-contact": "Póngase en contacto con el administrador de su organización o con KryptoGo para iniciar su plan de negocios.", "kgstudio.error.out-of-range": "El valor de entrada está fuera de rango.", "kgstudio.error.permission-denied.desc": "No tiene permiso para acceder a esta página.", "kgstudio.error.permission-denied.title": "<PERSON><PERSON><PERSON> den<PERSON>ado.", "kgstudio.error.please-try-again": "Vuelve a intentarlo más tarde", "kgstudio.error.resource-not-found.desc": "La página que estás buscando no existe.", "kgstudio.error.resource-not-found.title": "No se encontró el recurso.", "kgstudio.error.something-went-wrong": "Lo siento, algo salió mal", "kgstudio.error.try-again": "Se ha producido un error, inténtalo de nuevo más tarde.", "kgstudio.error.upload-attachment": "No se pudo cargar el archivo adjunto", "kgstudio.error.upload-image-failed": "No se pudo cargar la imagen", "kgstudio.error.upload-max-files": "Se ha superado el número máximo de archivos.", "kgstudio.error.upload-max-file-size": "El tamaño del archivo supera los 10 MB.", "kgstudio.error.upload-unsupported-file": "Formato de archivo no compatible.", "kgstudio.error.user-not-found": "No se ha encontrado el usuario.", "kgstudio.home.awaiting-approval-tx.title": "Transacciones en espera de aprobación", "kgstudio.home.awaiting-release-tx.title": "Transacciones en espera de publicación", "kgstudio.home.duplicate-org-name": "El nombre de la organización ya existe", "kgstudio.home.edit-organization.icon-hint1": "Formatos compatibles: JPG, PNG, WEBP, SVG.", "kgstudio.home.edit-organization.icon-hint2": "Tamaño recomendado: 100 x 100 px", "kgstudio.home.edit-organization.icon-hint3": "Máximo 10 MB", "kgstudio.home.edit-organization.icon-title": "Icono", "kgstudio.home.edit-organization.name-error1": "El nombre debe tener entre 2 y 20 caracteres", "kgstudio.home.edit-organization.name-error2": "El nombre contiene caracteres no válidos (@, #, $,/, *,...)", "kgstudio.home.edit-organization.name-placeholder": "Organización", "kgstudio.home.edit-organization.name-title": "Nombre", "kgstudio.home.edit-organization.success": "¡Se actualizó la configuración de la organización!", "kgstudio.home.edit-organization-title": "Editar organización", "kgstudio.home.joyride.step1": "En la página de configuración de la cuenta, puedes usar el agente de IA para crear una página de pago criptográfico o integrar la funcionalidad de pago en tu aplicación actual, adecuada para desarrolladores.", "kgstudio.home.joyride.step2": "En la página de la lista de productos, puedes crear tus propios productos y obtener un enlace de pago de una página para compartir con cualquier persona.", "kgstudio.home.joyride.step3": "En la página de tesorería, puedes ver los ingresos actuales de tu monedero y gestionar tu fondo de tesorería.", "kgstudio.home.joyride.step4": "También puede hacer clic en el botón aquí para visitar las páginas anteriores.", "kgstudio.home.joyride.step5": "¿Aún no tienes ningún producto? Haz clic aquí para crear tu primer producto.", "kgstudio.home.kyc-pending.title": "KYC pendiente de revisión", "kgstudio.home.kyc-pending.tooltip": "el número de solicitudes de KYC que aún no se han revisado", "kgstudio.home.my-pending-tx.title": "Mis transacciones pendientes", "kgstudio.home.orders-pending.title": "Pedidos pendientes", "kgstudio.home.orders-pending.tooltip": "el número de pedidos que aún no se han procesado", "kgstudio.home.payment.api-key-settings": "Configuración de claves de API", "kgstudio.home.payment.create-product": "Crear producto", "kgstudio.home.payment.data": "Datos de pago", "kgstudio.home.payment.manage-wallet": "Admini<PERSON><PERSON>", "kgstudio.home.payment.total-order": "Pedido total", "kgstudio.home.payment.total-revenue": "Ingresos totales", "kgstudio.home.payment.unique-customer": "Cliente único", "kgstudio.image.upload-required": "Por favor, sube la imagen", "kgstudio.kyc-status.pending": "Pendiente", "kgstudio.kyc-status.rejected": "<PERSON><PERSON><PERSON><PERSON>", "kgstudio.kyc-status.transfer-hint.pending": "Tenga en cuenta que la revisión de KYC para este usuario aún se está procesando. Por favor, complete la revisión lo más pronto posible y comercie con precaución.", "kgstudio.kyc-status.transfer-hint.rejected": "Tenga en cuenta que este usuario no ha pasado la revisión de KYC, por favor, comercie con precaución.", "kgstudio.kyc-status.transfer-hint.unverified": "Este usuario aún no ha pasado la verificación de KYC, por favor, comercie con precaución.", "kgstudio.kyc-status.transfer-hint.verified": "¡Este usuario ha pasado la verificación de KYC!", "kgstudio.kyc-status.unverified": "No verificado", "kgstudio.kyc-status.verified": "Verificado", "kgstudio.login.with-google": "", "kgstudio.message.input-message-content": "Introduce el contenido del mensaje", "kgstudio.nft.airdrop": "Lanzamiento aéreo", "kgstudio.nft.back-to-list": "Volver a la lista de proyectos", "kgstudio.nft.balance-and-fee": "El saldo de su billetera Polygon es {balance}POL, este proyecto necesitará aproximadamente {createCollectionFee}POL.", "kgstudio.nft.campaign": "Campaña", "kgstudio.nft.claimed": "RECLAMADO", "kgstudio.nft.claimed-total": "Reclamado//Total", "kgstudio.nft.collection-name-hint": "Introduzca caracteres alfanuméricos en un máximo de 40 caracteres.", "kgstudio.nft.collection-symbol-hint": "Sugerir un máximo de 10 letras, normalmente abreviaturas para el nombre de la colección", "kgstudio.nft.confirm-publish": "Confirmar publicación", "kgstudio.nft.create-collection-fee": "La creación de una colección de NFT requiere aproximadamente {createCollectionFee}POL.", "kgstudio.nft.delivery-method-title": "Método <PERSON>ga", "kgstudio.nft.edit-button-text": "<PERSON><PERSON>", "kgstudio.nft.end-date-title": "Fecha de finalización", "kgstudio.nft.error.collection-name-existed": "El nombre de la colección ya existe", "kgstudio.nft.error.project-not-found": "No se encontró el proyecto", "kgstudio.nft.favicon-title": "Favicon", "kgstudio.nft.form.banner-file-types": "Imágenes compatibles: JPG, PNG, GIF, SVG. Tamaño máximo: 100 MB. (Cambiar el nombre del formato del archivo no funcionará. Utilice los servicios de conversión de archivos si no tiene formatos de imagen compatibles).", "kgstudio.nft.form.banner-recommended-size": "Tamaño recomendado: 1400 x 350 px.", "kgstudio.nft.form.collection-description": "Descripción de NFT", "kgstudio.nft.form.collection-description-hint": "Hasta 1000 caracteres.", "kgstudio.nft.form.collection-name": "Nombre de la colección NFT", "kgstudio.nft.form.collection-name-hint": "Introduzca un máximo de 40 caracteres de texto alfanumérico.", "kgstudio.nft.form.contract-schema-name": "Nombre del esquema de contrato", "kgstudio.nft.form.favicon": "Imagen de favicon", "kgstudio.nft.form.favicon-image-title": "Imagen de favicon", "kgstudio.nft.form.max-supply": "Suministro máxi<PERSON>", "kgstudio.nft.form.max-supply-hint": "La emisión de NFT puede requerir una tarifa de gas: {mintFee} POL (varía según la actividad actual de la red).", "kgstudio.nft.form.max-supply-label": "Suministro total de NFT (suministro máximo)", "kgstudio.nft.form.mint-time-customized": "Hora personalizada", "kgstudio.nft.form.mint-time-end": "Hora de finalización de la recepción", "kgstudio.nft.form.mint-time-end-hint": "No se puede establecer una hora posterior a las 00:00 (UTC) del 01/01/2020.", "kgstudio.nft.form.mint-time-end-instant": "Nunca (termina el 01 de enero de 2038)", "kgstudio.nft.form.mint-time-instant": "No configurado (los NFT se pueden recibir inmediatamente después del lanzamiento)", "kgstudio.nft.form.mint-time-start": "Hora de inicio de recepción", "kgstudio.nft.form.nft-image": "Imagen NFT", "kgstudio.nft.form.nft-opensea-banner": "Banner NFT OpenSea", "kgstudio.nft.form.placeholder.description": "Proporciona una descripción detallada del artículo.", "kgstudio.nft.form.received-method": "Método de recepción", "kgstudio.nft.form.received-method-address": "Dirección de monedero vinculada", "kgstudio.nft.form.received-method-email": "Introduce tu correo", "kgstudio.nft.form.received-method-phone": "Introduce el número de teléfono", "kgstudio.nft.form.subtitle": "Subtítulo", "kgstudio.nft.form.symbol-name": "Nombre del símbolo NFT", "kgstudio.nft.form.symbol-name-hint": "Máximo: 10 caracteres. Solo letras en inglés. No puede contener espacios. A menudo se basa en el nombre de la colección, por ejemplo, KGYC", "kgstudio.nft.form.title": "<PERSON><PERSON><PERSON><PERSON> de <PERSON> página", "kgstudio.nft.form.upload-ico-desc": "Cargue el archivo.ico", "kgstudio.nft.form.upload-icon-file": "Cargue el archivo.ico", "kgstudio.nft.free-claim": "Reclamación gratuita NFT", "kgstudio.nft.go-to-project": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "kgstudio.nft.image-collection-item": "Imagen de colección NFT、Imagen del artículo NFT", "kgstudio.nft.image-file-types": "Formato compatible: JPG, PNG, GIF, SVG. Tamaño máximo: 10 MB (cambiar el nombre del formato de archivo no funcionará. Utilice los servicios de conversión de archivos si no tiene formatos de imagen compatibles).", "kgstudio.nft.image-recommended-size": "Tamaño recomendado: 2000 x 2000 px", "kgstudio.nft.info.contract": "Dirección del contrato", "kgstudio.nft.info.creator": "NFT Creador", "kgstudio.nft.info.na": "N/A", "kgstudio.nft.insufficient-balance": "¡Equilibrio insuficiente!", "kgstudio.nft.label.success-sms-preview": "Vista previa de SMS de éxito", "kgstudio.nft.mint-fee": "La tarifa de gas real para acuñar un NFT es aproximadamente {mintFee}POL (varía según la actividad actual de la red).", "kgstudio.nft.mint-page.na": "Aún no hay un enlace de actividad disponible.", "kgstudio.nft.mint-page-name": "Sitio web de la campaña NFT", "kgstudio.nft.mint-page.pending": "Generando el sitio web de su campaña... ¡el enlace de la campaña estará disponible en breve!", "kgstudio.nft.mint-page-title": "Página NFT Mint", "kgstudio.nft.mint-time": "Hora de la menta", "kgstudio.nft.modal.edit-mint-page": "Editar página de NFT Mint", "kgstudio.nft.next-step": "Próximo paso", "kgstudio.nft.nft-collection": "Colección NFT", "kgstudio.nft.nft-collection-chain": "El NFT se emitirá en la cadena Polygon.", "kgstudio.nft.nft-projects": "Proyectos NFT", "kgstudio.nft.notification.draft": "Estado del borrador: ¡Tu NFT aún no se ha publicado!", "kgstudio.nft.notification.failed": "¡Se ha producido un error al publicar tu NFT!", "kgstudio.nft.notification.pending": "El despliegue del contrato NFT está en progreso. Vuelva a consultar esta página más adelante.", "kgstudio.nft.notification.published": "¡Tu NFT se ha publicado correctamente!", "kgstudio.nft.overview": "Visión general", "kgstudio.nft.placeholder.success-sms": "Proporciona una descripción detallada del artículo.", "kgstudio.nft.preview.collection": "Vista previa de la colección NFT", "kgstudio.nft.preview.mint-page": "Vista previa: sitio web de la campaña NFT", "kgstudio.nft.prev-step": "Paso anterior", "kgstudio.nft.processing": "Procesando, espere", "kgstudio.nft.processing-description": "Se está implementando el contrato NFT, lo que puede tardar unos minutos. Mientras esperas a que se publique la NFT, puedes ir a la página del proyecto NFT para ver las actualizaciones o volver a la lista de proyectos de NFT para seguir navegando.", "kgstudio.nft.qr-code-title": "Código QR", "kgstudio.nft.recharge-balance": "Necesitas recargar {balanceDifference}POL en tu monedero para publicar el proyecto NFT.", "kgstudio.nft.reward": "Recompensa", "kgstudio.nft.save-draft": "Guardar borrador", "kgstudio.nft.saved-successfully-toast": "¡Se {collectionName}ha guardado el borrador del proyecto NFT ''!", "kgstudio.nft.scan-to-visit": "Escanear para visitar", "kgstudio.nft.start-date-title": "Fecha de inicio", "kgstudio.nft.status.claimed": "Reclamado", "kgstudio.nft.status.claim-rate": "Tasa de reclamaciones", "kgstudio.nft.status.draft": "<PERSON><PERSON><PERSON>", "kgstudio.nft.status.failed": "<PERSON><PERSON>", "kgstudio.nft.status.pending": "Pendiente", "kgstudio.nft.status.published": "Publicado", "kgstudio.nft.status.total": "Total", "kgstudio.nft.step.check": "Vista previa y confirmación", "kgstudio.nft.step.collection": "Complete la información básica para la recopilación de NFT", "kgstudio.nft.step.mint": "Configurar evento de distribución de NFT", "kgstudio.nft.subtitle-title": "Subtítulo", "kgstudio.nft.success-sms-title": "Notificación por SMS de éxito", "kgstudio.nft.text.success-sms": "【Notificación de recepción de NFT】~ ¡Bienvenido a recibir el {collectionName} NFT! Abra la aplicación KryptoGo Wallet e inicie sesión con su número de teléfono. {appLink}(Enlace de descarga de la aplicación:)", "kgstudio.nft.title-title": "<PERSON><PERSON><PERSON><PERSON>", "kgstudio.nft.total": "TOTAL", "kgstudio.nft.validation.collection-desc": "Introduce la descripción de la colección NFT", "kgstudio.nft.validation.collection-name": "Introduce el nombre de la colección NFT", "kgstudio.nft.validation.collection-name-max": "El nombre de la colección NFT no puede superar los 40 caracteres", "kgstudio.nft.validation.collection-name-min": "Introduce el nombre de la colección NFT", "kgstudio.nft.validation.enter-collection-abbreviation": "Introduzca la abreviatura de la colección NFT", "kgstudio.nft.validation.enter-total-supply": "Introduzca el suministro total de NFT Collection", "kgstudio.nft.validation.only-english-and-whitespace": "Solo se permiten caracteres en inglés y espacios en blanco", "kgstudio.nft.wallet-balance": "El saldo de tu monedero Polygon es", "kgstudio.nft.wallet-balance-matic": "POL. Asegúrese de que su saldo sea suficiente para cubrir la tasa de gas para crear y emitir NFT.", "kgstudio.onboarding.category-arts": "", "kgstudio.onboarding.category-arts-advice": "", "kgstudio.onboarding.category-arts-description": "", "kgstudio.onboarding.category-arts-name": "", "kgstudio.onboarding.category-educational": "", "kgstudio.onboarding.category-educational-advice": "", "kgstudio.onboarding.category-educational-description": "", "kgstudio.onboarding.category-educational-name": "", "kgstudio.onboarding.category-other": "", "kgstudio.onboarding.category-products": "", "kgstudio.onboarding.category-products-advice": "", "kgstudio.onboarding.category-products-description": "", "kgstudio.onboarding.category-products-name": "", "kgstudio.onboarding.check-store": "", "kgstudio.onboarding.close": "", "kgstudio.onboarding.copy-link": "", "kgstudio.onboarding.create-error": "", "kgstudio.onboarding.currency": "", "kgstudio.onboarding.fill-required-fields": "", "kgstudio.onboarding.go": "", "kgstudio.onboarding.image": "", "kgstudio.onboarding.next": "", "kgstudio.onboarding.prev": "", "kgstudio.onboarding.product-description": "", "kgstudio.onboarding.product-name": "", "kgstudio.onboarding.product-price": "", "kgstudio.onboarding.receive-address": "", "kgstudio.onboarding.revenue-over-3000": "", "kgstudio.onboarding.revenue-under-3000": "", "kgstudio.onboarding.skip": "", "kgstudio.onboarding.step1.title": "", "kgstudio.onboarding.step2.title": "", "kgstudio.onboarding.step3.title": "", "kgstudio.onboarding.step4.title": "", "kgstudio.onboarding.step6.title": "", "kgstudio.onboarding.step7.title": "", "kgstudio.onboarding.step8.description": "", "kgstudio.onboarding.step8.title": "", "kgstudio.onboarding.supported-chains": "", "kgstudio.operators.approval-threshold-desc": "La transferencia requiere aprobación una vez que alcance esta cantidad especificada.", "kgstudio.operators.daily-transfer-limit": "Límite de transferencia diario", "kgstudio.operators.edit-operator": "Operador de edición", "kgstudio.operators.page-title": "Operadores", "kgstudio.operators.placeholder": "Nombre de búsqueda, correo electrónico, ID", "kgstudio.operators.threshold-amount": "Importe límite", "kgstudio.operators.title": "Operadores de AssetPro", "kgstudio.operators.transfer-approval-threshold": "Umbral de aprobación de transferencias ", "kgstudio.operators.transfer-approval-threshold-desc": "Las transacciones que superen este límite requieren aprobación antes de enviarse.", "kgstudio.operators.transfer-limit-desc": "La cantidad máxima diaria que el operador puede transferir en AssetPro, que se restablece diariamente a las 00:00.", "kgstudio.operators.transfer-limit-error": "El importe límite no puede superar el límite de transferencia diario", "kgstudio.organization.create.back": "Atrás", "kgstudio.organization.create.back-to-login": "Volver al inicio de sesión", "kgstudio.organization.create.button": "Crear organización", "kgstudio.organization.create.email-description": "Este correo electrónico se asociará a tu cuenta en la organización.", "kgstudio.organization.create.email-placeholder": "Introduce tu dirección de correo electrónico", "kgstudio.organization.create.error.failed": "No se pudo crear la organización. Por favor, inténtelo de nuevo.", "kgstudio.organization.create.error.login-failed": "No se pudo iniciar sesión después de crear la organización. Vuelva a intentarlo.", "kgstudio.organization.create.error.missing-token": "Falta el token de autenticación. Intente iniciar sesión de nuevo.", "kgstudio.organization.create.login-success": "¡Se ha iniciado sesión correctamente!", "kgstudio.organization.create.org-name-placeholder": "Ingresa el nombre de la organización", "kgstudio.organization.create.subtitle.existing-user": "Crear una nueva organización", "kgstudio.organization.create.subtitle.new-user": "Crea tu organización para empezar", "kgstudio.organization.create.success": "La organización se creó correctamente", "kgstudio.overview.applications": "Aplicaciones", "kgstudio.overview.assetpro-intro": "Sistema de gestión de criptoactivos ligero y seguro.", "kgstudio.overview.compliance-intro": "Cumplimiento global, adaptable a los cambios normativos.", "kgstudio.overview.create-date": "Fecha de creación", "kgstudio.overview.nft-intro": "Sin código, crea fácilmente campañas de NFT.", "kgstudio.overview.no-access": "Parece que no tienes permiso para este módulo. Ponte en contacto con el propietario del sistema para que te ayude a activarlo si quieres usarlo.", "kgstudio.overview.user360-intro": "Tu centro de comando de Web3.", "kgstudio.overview.wallet-intro": "Monedero exclusivo de la marca y centrado en el usuario.", "kgstudio.page.input-page-subtitle": "Introduzca el subtítulo de la página", "kgstudio.page.input-page-title": "Introduzca el título de la página", "kgstudio.page.page-size-description": "Mostrar {pageSize}filas por página", "kgstudio.payment.accent-color": "", "kgstudio.payment.add-field": "Agregar campo", "kgstudio.payment.aggregated-amount": "Importe agregado", "kgstudio.payment.all-clients": "Todos los clientes", "kgstudio.payment.amount": "Monto", "kgstudio.payment.button-preview": "Vista previa del botón", "kgstudio.payment.callback-dashboard": "Panel de Callback", "kgstudio.payment.callback-details": "Detalles de la devolución de llamada", "kgstudio.payment.callback-id": "ID de devolución de llamada", "kgstudio.payment.callback-payload": "Carga útil de devolución de llamada", "kgstudio.payment.callback-result": "<PERSON><PERSON><PERSON><PERSON>", "kgstudio.payment.callback-status": "<PERSON><PERSON><PERSON>", "kgstudio.payment.callback-type": "<PERSON><PERSON><PERSON>", "kgstudio.payment.callback-url": "URL de devolución de llamada", "kgstudio.payment.callback-url-placeholder": "Enviaremos el resultado del pago a esta URL", "kgstudio.payment.chain-id": "", "kgstudio.payment.chain-id-desc": "", "kgstudio.payment.client-id": "ID de cliente", "kgstudio.payment.column-setting": "Columnas", "kgstudio.payment.copy-button": "Botón Copiar pago", "kgstudio.payment.copy-link": "<PERSON><PERSON><PERSON> enlace de pago", "kgstudio.payment.create-item-error": "No se pudo crear el artículo de pago", "kgstudio.payment.create-item-success": "El artículo de pago se creó correctamente", "kgstudio.payment.create-payment": "Crear pago", "kgstudio.payment.create-product": "Crear producto", "kgstudio.payment.create-product-title": "Crear producto", "kgstudio.payment.create-payment-intent": "Crear intención de pago", "kgstudio.payment.create-intent-success": "Intención de pago creada exitosamente", "kgstudio.payment.create-intent-error": "Error al crear la intención de pago", "kgstudio.payment.payment-intent-details": "Detalles de la intención de pago", "kgstudio.payment.configure-payment-intent-details": "Configurar los detalles de la intención de pago", "kgstudio.payment.crypto-amount": "<PERSON><PERSON> crip<PERSON>grá<PERSON>", "kgstudio.payment.crypto-price": "<PERSON><PERSON> criptográfico", "kgstudio.payment.currency": "Moneda", "kgstudio.payment.custom-fields": "Campos personalizados", "kgstudio.payment.date-range": "Intervalo de fechas", "kgstudio.payment.deadline": "<PERSON><PERSON> lí<PERSON>", "kgstudio.payment.delete-item-confirmation": "¿Estás seguro de que deseas eliminar este elemento de pago?", "kgstudio.payment.delete-item-title": "Eliminar elemento de pago", "kgstudio.payment.duration": "Duración", "kgstudio.payment.edit-product-title": "Editar producto", "kgstudio.payment.error-loading-callbacks": "No se pudieron cargar los registros de Callback", "kgstudio.payment.error-loading-oauth-clients": "Error al cargar los clientes de OAuth", "kgstudio.payment.error-url": "URL de error", "kgstudio.payment.error-url-placeholder": "Introduzca la URL de redireccionamiento del error", "kgstudio.payment.event-details": "Detalles del evento", "kgstudio.payment.event-type": "Tipo de evento", "kgstudio.payment.export-csv": "Exportar CSV", "kgstudio.payment.failed": "<PERSON><PERSON>", "kgstudio.payment.fiat-amount": "Monto de Fiat", "kgstudio.payment.fiat-currency": "Moneda Fiat", "kgstudio.payment.field-key": "Clave", "kgstudio.payment.field-label": "Etiqueta de campo", "kgstudio.payment.field-label-required": "La etiqueta de campo es obligatoria", "kgstudio.payment.field-name": "Nombre de campo", "kgstudio.payment.field-name-duplicate": "", "kgstudio.payment.field-name-required": "El nombre del campo es obligatorio", "kgstudio.payment.field-type": "Tipo de campo", "kgstudio.payment.field-value": "Valor", "kgstudio.payment.group-key": "Clave de grupo", "kgstudio.payment.group-key-search-placeholder": "Búsqueda por clave de grupo", "kgstudio.payment.http-code": "Código HTTP", "kgstudio.payment.intent-id": "ID de intención", "kgstudio.payment.kg-deep-link": "KG Deep Link", "kgstudio.payment.merchant-email": "Correo electrónico del vendedor", "kgstudio.payment.merchant-settings": "Configuración del comerciante", "kgstudio.payment.merchant-settings-desc": "El correo electrónico del vendedor y el color de acento aparecen en la página de pago. Los clientes pueden ponerse en contacto con el comerciante por correo electrónico para solicitar asistencia, y el color de acento resalta los elementos clave.", "kgstudio.payment.new-field": "Campo nuevo", "kgstudio.payment.open-page": "<PERSON><PERSON>r página de pago", "kgstudio.payment.optional-field": "Campo opcional", "kgstudio.payment.optional-fields": "Campos opcionales", "kgstudio.payment.order-data": "Datos del pedido", "kgstudio.payment.order-data-expanded": "Ampliar los datos del pedido", "kgstudio.payment.order-data-fields": "Campos de datos del pedido", "kgstudio.payment.organization-icon": "Icono de organización", "kgstudio.payment.organization-icon-placeholder": "Introducir la URL del icono de la organización", "kgstudio.payment.organization-id": "ID de la organización", "kgstudio.payment.organization-id-placeholder": "Ingresa el ID de la organización", "kgstudio.payment.organization-name": "Nombre de la organización", "kgstudio.payment.organization-name-placeholder": "Ingresa el nombre de la organización", "kgstudio.payment.organization-required": "Se requiere organización", "kgstudio.payment.recipient-details": "Detalles del destinatario", "kgstudio.payment.recipient-name": "Nombre del destinatario", "kgstudio.payment.recipient-name-placeholder": "Nombre de la organización", "kgstudio.payment.contact-email": "Correo de contacto", "kgstudio.payment.contact-email-placeholder": "<EMAIL>", "kgstudio.payment.your-details": "<PERSON><PERSON>", "kgstudio.payment.your-organization-name": "Nombre de tu organización", "kgstudio.payment.your-organization-contact-email": "Correo de contacto de tu organización", "kgstudio.payment.payout-details": "Detalles del pago", "kgstudio.payment.payout-due-date": "Fecha de vencimiento del pago", "kgstudio.payment.payout-due-date-placeholder": "YYYY/MM/DD", "kgstudio.payment.description-placeholder": "Ingresa el ID de la factura o cualquier otra descripción sobre este pago", "kgstudio.payment.attachment-label": "Adjunto", "kgstudio.payment.upload-attachment-desc": "Sube la factura o cualquier adjunto aquí", "kgstudio.payment.payout-method": "Método de pago", "kgstudio.payment.network": "Red", "kgstudio.payment.token": "Token", "kgstudio.payment.recipient-receiving-wallet": "Billetera receptora del destinatario", "kgstudio.payment.recipient-wallet-placeholder": "0xDaF09F6E6cEa6E08f4c7C32D4f71b54bdA02913", "kgstudio.payment.payout-amount": "Cantidad del pago", "kgstudio.payment.payout-amount-placeholder": "2000", "kgstudio.payment.payout-summary": "Resumen del pago", "kgstudio.payment.kryptogo-handling-fee": "Tarifa de manejo de KryptoGO (5%)", "kgstudio.payment.total-amount": "Cantidad total", "kgstudio.payment.validation.recipient-name-required": "Se requiere el nombre del destinatario", "kgstudio.payment.validation.email-valid": "Se requiere un correo válido", "kgstudio.payment.validation.organization-name-required": "Se requiere el nombre de la organización", "kgstudio.payment.validation.due-date-required": "Se requiere la fecha de vencimiento", "kgstudio.payment.validation.description-required": "Se requiere la descripción", "kgstudio.payment.validation.network-required": "Se requiere la red", "kgstudio.payment.validation.token-required": "Se requiere el token", "kgstudio.payment.validation.recipient-wallet-required": "Se requiere la dirección de la billetera del destinatario", "kgstudio.payment.validation.payout-amount-required": "Se requiere la cantidad del pago", "kgstudio.payment.payer-address": "Dirección del pagador", "kgstudio.payment.payment-chain-id": "ID de cadena de pago", "kgstudio.payment.payment-failed": "Fallo en el pago", "kgstudio.payment.payment-intent-id": "ID de intención de pago", "kgstudio.payment.payment-item-list": "Producto", "kgstudio.payment.payment-link": "Enlace de pago", "kgstudio.payment.payment-list": "Historial de pedidos", "kgstudio.payment.payout-list": "Historial de pagos", "kgstudio.payment.payment-status": "Estado del pago", "kgstudio.payment.payment-success": "<PERSON><PERSON>oso", "kgstudio.payment.payment-text": "Cliente", "kgstudio.payment.payment-tx-hash": "Hash TX de pago", "kgstudio.payment.pay-token": "Token de pago preferido", "kgstudio.payment.pending": "Pendiente", "kgstudio.payment.pricing-mode": "<PERSON><PERSON>", "kgstudio.payment.pricing-mode-fiat": "Moneda fiduciaria", "kgstudio.payment.pricing-mode-crypto": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kgstudio.payment.pricing-mode-dynamic": "Dinámica", "kgstudio.payment.pricing-mode-fixed": "<PERSON><PERSON>", "kgstudio.payment.product-currency": "Moneda", "kgstudio.payment.product-description": "Descripción del producto", "kgstudio.payment.product-description-placeholder": "Introduce la descripción del producto", "kgstudio.payment.product-image": "Imagen del producto", "kgstudio.payment.product-name": "Nombre del producto", "kgstudio.payment.product-name-placeholder": "Introduce el nombre del producto", "kgstudio.payment.product-price": "Precio del producto", "kgstudio.payment.product-price-placeholder": "Introduce el precio del producto", "kgstudio.payment.received-amount": "<PERSON><PERSON> recibido", "kgstudio.payment.refund": "Reembolso", "kgstudio.payment.refund.address.placeholder": "Ingresa la dirección", "kgstudio.payment.refund.address.text": "Dirección de reembolso", "kgstudio.payment.refund-amount": "Importe del reembolso", "kgstudio.payment.refund.amount.placeholder": "Ingresa la cantidad", "kgstudio.payment.refund.amount.text": "Monto", "kgstudio.payment.refund.button": "Reembolso", "kgstudio.payment.refund.cancel": "<PERSON><PERSON><PERSON>", "kgstudio.payment.refund.confirm": "Confirme el reembolso", "kgstudio.payment.refund.error": "Error al iniciar el reembolso", "kgstudio.payment.refund.success": "El reembolso se inició correctamente", "kgstudio.payment.refund.title": "Pago de reembolso", "kgstudio.payment.remove-field": "Eliminar", "kgstudio.payment.required-field": "Campo obligatorio", "kgstudio.payment.required-fields": "Campos obligatorios", "kgstudio.payment.required-fields-description": "Estos campos son obligatorios para crear la intención de pago", "kgstudio.payment.optional-fields-description": "Campos adicionales que se pueden incluir", "kgstudio.payment.amount-fiat-placeholder": "100.00", "kgstudio.payment.amount-crypto-placeholder": "0.1", "kgstudio.payment.result-failed": "<PERSON><PERSON>", "kgstudio.payment.result-success": "Éxito", "kgstudio.payment.search-intent": "Buscar por ID de intención", "kgstudio.payment.select-client": "Seleccione el cliente", "kgstudio.payment.select-client-first": "Seleccione primero un cliente", "kgstudio.payment.sending": "Enviando...", "kgstudio.payment.send-test": "<PERSON><PERSON><PERSON> prueba", "kgstudio.payment.sent": "Enviado", "kgstudio.payment.sign-payload": "Carga útil de señales", "kgstudio.payment.status": "Estado", "kgstudio.payment.status-code": "Código de estado", "kgstudio.payment.status-completed": "Completado", "kgstudio.payment.status-expired": "<PERSON><PERSON><PERSON>", "kgstudio.payment.status-failed": "<PERSON><PERSON>", "kgstudio.payment.status-pending": "Pendiente", "kgstudio.payment.status-refunded": "Reembolsado", "kgstudio.payment.success": "Éxito", "kgstudio.payment.success-message": "", "kgstudio.payment.success-message-desc": "", "kgstudio.payment.success-message-placeholder": "", "kgstudio.payment.success-url": "URL de éxito", "kgstudio.payment.success-url-placeholder": "Ingresa la URL de redireccionamiento exitoso", "kgstudio.payment.symbol": "Símbolo", "kgstudio.payment.test": "Prueba", "kgstudio.payment.test-callback": "<PERSON><PERSON><PERSON>", "kgstudio.payment.test-callback-error": "No se pudo enviar la Callback", "kgstudio.payment.test-callback-sent": "La Callback se envió correctamente", "kgstudio.payment.timestamp": "Marca de tiempo", "kgstudio.payment.type": "Tipo", "kgstudio.payment.type-payment": "Pago", "kgstudio.payment.type-test": "Prueba", "kgstudio.payment.update-item-error": "No se pudo actualizar el elemento de pago", "kgstudio.payment.update-item-success": "El elemento de pago se actualizó correctamente", "kgstudio.payment.upload-image-desc": "Subir imagen", "kgstudio.payment.url-copied": "<PERSON>lace copiado", "kgstudio.payment.url-hint": "Al redirigir, agregaremos «? pid=payment_intent_id» a esta URL", "kgstudio.payment.view-payment-page": "Compartir página de pago", "kgstudio.payment.webhook-url": "URL del webhook", "kgstudio.payment.connect-wallet": "Conectar Billetera", "kgstudio.payment.wallet-connected": "Billetera Conectada", "kgstudio.payment.wallet-disconnected": "Billetera Desconectada", "kgstudio.payment.wallet-connection-failed": "Error de Conexión de Billetera", "kgstudio.payment.disconnect-wallet": "Desconectar Billetera", "kgstudio.payment.copy-address": "<PERSON><PERSON><PERSON>", "kgstudio.payment.address-copied": "Dirección Copiada", "kgstudio.payment.select-wallet-description": "Elija una billetera para conectarse a la aplicación", "kgstudio.payment.metamask-description": "Conectar usando la extensión del navegador MetaMask", "kgstudio.payment.browser-wallet-description": "Conectar usando billetera del navegador", "kgstudio.payment.wallet-description": "Conectar usando esta billetera", "kgstudio.payment.connecting": "Conectando...", "kgstudio.payment.not-available": "No Disponible", "kgstudio.payment.wallet-security-notice": "Solo conéctese con sitios en los que confíe", "kgstudio.permissions.notification-description": "Puede ver los usuarios que tienen permiso para acceder al sistema. Si desea editar sus usuarios, póngase en contacto con el proveedor del sistema.", "kgstudio.permissions.title": "Usuarios", "kgstudio.permissions.user-id": "ID de usuario", "kgstudio.review.ai-summary": "Resumen de IA", "kgstudio.review.ai-summary-fail": "No hay noticias negativas relacionadas con este cliente y no se puede realizar un resumen de IA.", "kgstudio.review.aml-risk": "Riesgo de AML", "kgstudio.review.btn": "Rev<PERSON><PERSON>", "kgstudio.review.case-details": "Detalles del caso", "kgstudio.review.check-id": "Verificar ID", "kgstudio.review.created": "<PERSON><PERSON><PERSON>", "kgstudio.review.details": "Detalles", "kgstudio.review.filter-placeholder": "Nombre legal, teléfono, correo electrónico, identificación nacional, ID de línea", "kgstudio.review.high-risk": "Alto riesgo", "kgstudio.review.idv": "IDV", "kgstudio.review.id-verification": "Verificación de ID", "kgstudio.review.idv-fail": "<PERSON><PERSON>", "kgstudio.review.idv-pass": "Pase", "kgstudio.review.internal-note": "Nota interna", "kgstudio.review.internal-notes-length-limit": "Las notas internas pueden tener un máximo de 50 caracteres", "kgstudio.review.internal-notes-required": "Las notas internas son obligatorias cuando el estado es «rechazado»", "kgstudio.review.kyc-status": "Estado de KYC", "kgstudio.review.latest-submit": "Última hora de envío", "kgstudio.review.low-risk": "<PERSON>jo riesgo", "kgstudio.review.mid-risk": "Riesgo medio", "kgstudio.review.name": "Nombre", "kgstudio.review.name-screening": "Selección de nombres", "kgstudio.review.personal-information": "Información personal", "kgstudio.review.process-ai": "Procesamiento de resúmenes de IA...", "kgstudio.review.processing": "Procesamiento", "kgstudio.review.review-detail": "Detalles de la revisión", "kgstudio.review.reviewer": "Revisor", "kgstudio.review.review-result": "Resultado de la revisión", "kgstudio.review.review-time": "Tiempo de revisión", "kgstudio.review.risk": "Riesgo", "kgstudio.review.sanctioned": "Sancionado", "kgstudio.review.sanctioned-false": "<PERSON><PERSON><PERSON>", "kgstudio.review.sanctioned-true": "<PERSON><PERSON><PERSON>", "kgstudio.review.start-ai": "Resumen de Start AI", "kgstudio.review.submission": "Sumisión", "kgstudio.review.summary": "Resumen", "kgstudio.review.updated": "Actualizado en", "kgstudio.search.email": "Buscar correo electrónico", "kgstudio.select.at-least-one": "Selecciona al menos un artículo", "kgstudio.send.add-attachment": "Agregar archivo adjunto", "kgstudio.send.attachment-required": "Requerir un archivo adjunto para su aprobación", "kgstudio.send.by-email": "Email", "kgstudio.send.by-phone-number": "Por número de teléfono", "kgstudio.send.do-not-leave-page": "No salgas de esta página.", "kgstudio.send.gasless-alert-button": "", "kgstudio.send.gasless-alert-desc": "", "kgstudio.send.gasless-alert-title": "", "kgstudio.send.gasless-modal-deducted-desc-1": "", "kgstudio.send.gasless-modal-deducted-desc-2": "", "kgstudio.send.gasless-modal-deducted-title": "", "kgstudio.send.gasless-modal-desc": "", "kgstudio.send.gasless-modal-full-title": "", "kgstudio.send.gasless-modal-title": "", "kgstudio.send.gas-token-balance": "Saldo de fichas de gas: {balance}(Tarifa de gas estimada:{gasFee})", "kgstudio.send.loading-hint": "La transacción puede tardar más de lo esperado si la cadena de bloques está muy ocupada. Para comprobar el estado y los detalles de la transacción, haz clic en el hash de Tx.", "kgstudio.send.max": "", "kgstudio.send.note-placeholder": "Ingresa la nota de la transacción y carga los archivos adjuntos necesarios para su aprobación.", "kgstudio.send.note-required": "Requerir nota de transacción para su aprobación", "kgstudio.send.over-limit": "Por encima del límite", "kgstudio.send.remaining-balance": "Saldo transferible restante en la actualidad: ", "kgstudio.send.remaining-balance-today": "El saldo restante hoy es de {formattedCurrentLimit}U (con un límite diario de {formattedDailyLimit}U).", "kgstudio.send.review-note": "Nota de revisión", "kgstudio.send.send-confirm-alert": "Una vez que se confirme esta transacción, ¡se hará efectiva de inmediato y no se podrá revertir! Asegúrese de que la cadena de bloques, el importe y el destinatario sean correctos.", "kgstudio.send.send-confirm-submit-desc-alert": "Asegúrese de que los detalles de la transacción sean correctos, ya que no se pueden modificar después del envío.", "kgstudio.send.send-confirm-submit-title-alert": "Esta transacción se enviará solo después de la aprobación.", "kgstudio.send.send-to": "Enviar a", "kgstudio.send.submit-request": "<PERSON><PERSON><PERSON> solicitud", "kgstudio.send.title": "Enviar fondo", "kgstudio.send.to-user": "al usuario", "kgstudio.send.transaction-attachment": "Apego", "kgstudio.send.transaction-error-desc": "Vuelva a intentarlo después de que el administrador haya depositado suficientes activos", "kgstudio.send.transaction-error-title": "Error: inventario insuficiente", "kgstudio.send.transaction-note": "Nota de transacción", "kgstudio.send.transaction-submit-success-desc": "Su transacción se ha enviado y se ejecutará una vez que se apruebe y publique.", "kgstudio.send.transaction-submit-success-title": "Transacción enviada", "kgstudio.send.transaction-success-desc": "Su solicitud de transacción se ha enviado correctamente y pronto podrá ver los resultados de la transacción.", "kgstudio.send.transaction-success-title": "Transacción enviada correctamente", "kgstudio.send.tx-failed": "Transacción fallida", "kgstudio.send.tx-failed-description": "No se pudo transferir los fondos. Vuelva a intentarlo o póngase en contacto con el administrador del sistema.", "kgstudio.send.tx-in-progress": "Transacción en curso.", "kgstudio.send.tx-success": "¡Transacción exitosa!", "kgstudio.send.view-profile": "Ver perfil", "kgstudio.setting.api-keys": "Claves de API", "kgstudio.setting.create-api-key": "<PERSON><PERSON>r clave de <PERSON>", "kgstudio.setting.create-first-api-key": "Crea tu primera clave de API para empezar", "kgstudio.setting.create-first-oauth-client": "Crea tu primer cliente de OAuth para empezar", "kgstudio.setting.create-oauth-client": "Crear cliente OAuth", "kgstudio.setting.no-api-keys": "Sin claves de API", "kgstudio.setting.no-oauth-clients": "No hay clientes de OAuth", "kgstudio.setting.oauth-clients": "Clientes de OAuth", "kgstudio.setting.org-settings": "Configuración de la organización", "kgstudio.setting.user.account-settings": "Configuración de la cuenta", "kgstudio.setting.user.add-address": "Agregar <PERSON>", "kgstudio.setting.user.add-new-address": "Agregar nueva dirección", "kgstudio.setting.user.address-name": "Nombre de la dirección", "kgstudio.setting.user.address-name-placeholder": "p. ej., My Main Wallet", "kgstudio.setting.user.api-keys": "Claves de API", "kgstudio.setting.user.api-keys-description": "Las claves de API permiten el acceso programático a su cuenta de KryptoGo Studio. Deben mantenerse seguras y nunca compartirse públicamente.", "kgstudio.setting.user.cancel": "<PERSON><PERSON><PERSON>", "kgstudio.setting.user.check-documentation": "Compruebe la documentación", "kgstudio.setting.user.client-domain": "<PERSON>inio", "kgstudio.setting.user.client-id": "ID de cliente", "kgstudio.setting.user.client-id-desc": "Si no tienes un ID de cliente, agrégalo en la página [Configuración de la cuenta -> Clientes de OAuth].", "kgstudio.setting.user.client-id-required": "Se requiere el ID de cliente", "kgstudio.setting.user.client-name": "Nombre del cliente", "kgstudio.setting.user.client-secret": "Secreto del cliente", "kgstudio.setting.user.client-type": "Tipo", "kgstudio.setting.user.close": "<PERSON><PERSON><PERSON>", "kgstudio.setting.user.confirm-delete-api-key": "¿Estás seguro de que deseas eliminar esta clave de API? Esta acción no se puede deshacer.", "kgstudio.setting.user.confirm-delete-oauth-client": "¿Estás seguro de que quieres eliminar este cliente de OAuth? Esta acción no se puede deshacer.", "kgstudio.setting.user.confirm-deletion": "Confirmar la eliminación", "kgstudio.setting.user.copy-prompt": "<PERSON><PERSON><PERSON>", "kgstudio.setting.user.create-api-key": "<PERSON><PERSON>r clave de <PERSON>", "kgstudio.setting.user.create-client-title": "Crear cliente OAuth", "kgstudio.setting.user.created": "<PERSON><PERSON><PERSON>", "kgstudio.setting.user.create-first-api-key": "Crea tu primera clave de API para empezar", "kgstudio.setting.user.create-first-oauth-client": "Crea tu primer cliente de OAuth para empezar", "kgstudio.setting.user.create-key-title": "<PERSON><PERSON>r clave de <PERSON>", "kgstudio.setting.user.create-oauth-client": "Crear cliente OAuth", "kgstudio.setting.user.default": "Predeterminado", "kgstudio.setting.user.delete": "Bo<PERSON>r", "kgstudio.setting.user.description": "Descripción", "kgstudio.setting.user.domain-format-note": "El dominio debe empezar por http://o https://(p. ej., https://app.kryptogo.com)", "kgstudio.setting.user.edit": "<PERSON><PERSON>", "kgstudio.setting.user.error.add-address": "Error de formato, no se puede agregar la dirección de pago de EVM", "kgstudio.setting.user.error.copy-clipboard": "No se pudo copiar al portapapeles", "kgstudio.setting.user.error.create-api-key": "No se pudo crear la clave de API", "kgstudio.setting.user.error.create-oauth-client": "No se pudo crear el cliente OAuth", "kgstudio.setting.user.error.delete-api-key": "No se pudo eliminar la clave de API", "kgstudio.setting.user.error.domain-format": "El dominio debe empezar por http://o https://", "kgstudio.setting.user.error.domain-required": "El dominio es obligatorio", "kgstudio.setting.user.error.fetch-api-keys": "No se pudieron obtener las claves de API", "kgstudio.setting.user.error.fetch-oauth-clients": "No se pudieron recuperar los clientes de OAuth", "kgstudio.setting.user.error.set-default-address": "No se pudo establecer la dirección predeterminada", "kgstudio.setting.user.error.update-oauth-client": "No se pudo actualizar el cliente OAuth", "kgstudio.setting.user.joyride.back": "Atrás", "kgstudio.setting.user.joyride.close": "<PERSON><PERSON><PERSON>", "kgstudio.setting.user.joyride.finish": "Finalizar", "kgstudio.setting.user.joyride.next": "Próxima", "kgstudio.setting.user.joyride.skip": "Saltar", "kgstudio.setting.user.joyride.step1": "El ID de cliente de OAuth (ID de cliente) le permite integrar su aplicación con el servicio KryptoGO Payment SDK. También puede crear su propio ID de cliente para integrarlo en diferentes aplicaciones.", "kgstudio.setting.user.joyride.step2": "Copie el mensaje de AI con un solo clic y péguelo en el servicio modelo de AI (recomendado: claude-3.7-sonnect, gpt-o3) para generar una página web de pagos criptográficos que pueda usarse de inmediato.", "kgstudio.setting.user.key-description": "Descripción (opcional)", "kgstudio.setting.user.key-name": "Nombre", "kgstudio.setting.user.key-prefix": "Key prefix", "kgstudio.setting.user.last-used": "Utilizado por última vez", "kgstudio.setting.user.loading": "Cargando...", "kgstudio.setting.user.logo": "Logo", "kgstudio.setting.user.manage-payment-addresses": "Administrar direcciones de pago", "kgstudio.setting.user.no-api-keys": "Sin claves de API", "kgstudio.setting.user.no-oauth-clients": "No hay clientes de OAuth", "kgstudio.setting.user.oauth-clients": "Clientes de OAuth", "kgstudio.setting.user.oauth-clients-description": "Los clientes de OAuth le permiten integrar sus aplicaciones con los servicios de KryptoGo. Cada cliente tiene una identificación y un secreto únicos que deben mantenerse seguros.", "kgstudio.setting.user.org-wallet": "Monedero de organización", "kgstudio.setting.user.payment-address-description": "La dirección de pago se usa para recibir los pagos de los clientes.", "kgstudio.setting.user.save-client-message": "Esta es la única vez que verás el secreto del cliente. Cópielo y guárdelo de forma segura. Los secretos de los clientes se utilizan para autenticar su aplicación cuando realiza solicitudes de API a los servicios de KryptoGO.", "kgstudio.setting.user.save-client-title": "Importante: Guarda tus credenciales de cliente", "kgstudio.setting.user.save-key-message": "Esta es la única vez que verás esta clave de API. Cópiala y guárdala de forma segura.", "kgstudio.setting.user.save-key-title": "Importante: Guarda tu clave de API", "kgstudio.setting.user.set-as-default": "Establecer como predeterminado", "kgstudio.setting.user.success.address-added": "¡La dirección de pago de EVM se agregó correctamente!", "kgstudio.setting.user.success.api-key-created": "La clave de API se creó correctamente", "kgstudio.setting.user.success.api-key-deleted": "La clave de API se ha eliminado correctamente", "kgstudio.setting.user.success.copied": "{item}copiado al portapapeles", "kgstudio.setting.user.success.oauth-client-created": "El cliente OAuth se creó correctamente", "kgstudio.setting.user.success.oauth-client-updated": "El cliente OAuth se actualizó correctamente", "kgstudio.setting.user.success.org-wallet-default": "Monedero de organización establecido como predeterminado", "kgstudio.setting.user.update-oauth-client": "Actualizar el cliente OAuth", "kgstudio.setting.user.wallet-address": "Dirección del monedero", "kgstudio.setting.user.your-addresses": "Sus direcciones", "kgstudio.team.delete.description": "¿Está seguro de que desea desactivar \"{currentMemberName}({currentMemberEmail})»? Se cerrará la sesión de inmediato y no podrán volver a iniciar sesión.", "kgstudio.team.delete.success": "«{name}({email})» se ha eliminado de tu equipo.", "kgstudio.team.edit.title": "Editar miembro del equipo", "kgstudio.team.invite.error.existed": "El usuario ya existe", "kgstudio.team.invite.error.rate-limit": "Has alcanzado el límite de invitaciones en un breve período de tiempo. Vuelve a intentarlo más tarde.", "kgstudio.team.invite.sent": "¡Invitación enviada!", "kgstudio.team.invite.text": "Invitar", "kgstudio.team.invite.title": "Invitar a un miembro del equipo", "kgstudio.team.member.member-id.title": "ID de miembro (opcional)", "kgstudio.team.member.name.placeholder": "por ejemplo: <PERSON>", "kgstudio.team.members": "Miembros del equipo", "kgstudio.team.name.validation.required": "El nombre no puede estar vacío", "kgstudio.team.permission-settings": "Configuración de permisos", "kgstudio.team.rate-limit.hint": "Vuelva a invitar después de 1 minuto", "kgstudio.team.remove-member": "Desactivar miembro", "kgstudio.team.resend": "Reenviar invitación", "kgstudio.team.role.approver": "Aprobador", "kgstudio.team.role.aseet-pro": "Función AssetPro", "kgstudio.team.role.asset-pro": "Función AssetPro", "kgstudio.team.role.compliance": "Función de cumplimiento", "kgstudio.team.role-decription.aseet-pro.admin": "tiene acceso completo a AssetPro, lo que incluye ver la tesorería, enviar activos, aprobar, descargar todo el historial de transacciones y configurar miembros", "kgstudio.team.role-decription.aseet-pro.approver": "puede ver todas las transacciones en AssetPro y aprobar o rechazar las transacciones pendientes de aprobación.", "kgstudio.team.role-decription.aseet-pro.finance-manager": "puede ver todas las transacciones en AssetPro, liberar o rechazar las transacciones pendientes de publicación y ver la tesorería.", "kgstudio.team.role-decription.aseet-pro.trader": "pueden enviar activos con un límite diario y ver sus propias transacciones", "kgstudio.team.role-decription.compliance.admin": "tiene acceso completo al cumplimiento, incluida la revisión y la configuración global del proceso de verificación", "kgstudio.team.role-decription.compliance.reviewer": "solo puede revisar y editar los casos de cumplimiento", "kgstudio.team.role.description": "El propietario tiene acceso completo a todas las funciones de los módulos a los que se suscribe su organización.", "kgstudio.team.role.finance-manager": "Gerente de finanzas", "kgstudio.team.role.nft-boost": "Función NFT Boost", "kgstudio.team.role.owner": "<PERSON><PERSON>", "kgstudio.team.role.reviewer": "Revisor", "kgstudio.team.role.title": "Rol del equipo", "kgstudio.team.role.trader": "Comerciante", "kgstudio.team.role.user360": "Rol User 360", "kgstudio.team.role.validation": "Seleccione al menos un rol de módulo", "kgstudio.team.role.wallet": "Función de cartera", "kgstudio.team.text": "Equipo", "kgstudio.time.end-after-start": "La hora de finalización debe ser posterior a la hora de inicio", "kgstudio.time.input-time": "Introduce la hora", "kgstudio.transaction.export": "Exportación", "kgstudio.transaction.operator": "Operador", "kgstudio.transaction.placeholder": "Correo electrón<PERSON>, teléfono, dirección y hash de TX del destinatario", "kgstudio.transactions.recipient-placeholder": "Correo electrón<PERSON>, teléfono, dirección", "kgstudio.transaction.status-awaiting-approval": "En espera de aprobación", "kgstudio.transaction.status-awaiting-release": "En espera de publicación", "kgstudio.transaction.status-failed": "Error en el envío", "kgstudio.transaction.status-pending": "Aprobación pendiente ", "kgstudio.transaction.status-reject": "<PERSON><PERSON><PERSON><PERSON>", "kgstudio.transaction.status-rejected": "<PERSON><PERSON><PERSON><PERSON>", "kgstudio.transaction.status-sending": "Enviando", "kgstudio.transaction.status-success": "Enviar éxito", "kgstudio.transactions.title": "Historial de transacciones", "kgstudio.transaction.submit-time": "<PERSON>ra de env<PERSON>", "kgstudio.transactions.user-placeholder": "ID, dirección", "kgstudio.treasury.add-fund": "Agregar fondo", "kgstudio.treasury.add-fund-modal.desc": "Escanea el código QR para enviar tus activos a esta dirección de monedero", "kgstudio.treasury.agree-and-continue": "Comprendo los riesgos", "kgstudio.treasury.asset": "Activo", "kgstudio.treasury.asset-name": "Nombre del activo", "kgstudio.treasury.buy-crypto.desc": "Comprar Crypto Desc", "kgstudio.treasury.buy-crypto.margin": "Margen de beneficio de comprar c<PERSON> (%)", "kgstudio.treasury.buy-crypto-title": "Comp<PERSON> crip<PERSON>", "kgstudio.treasury.chart": "Gráfico", "kgstudio.treasury.click-to-refresh": "Haga clic para actualizar", "kgstudio.treasury.click-to-reveal": "Haz clic en el botón de abajo para ver tu clave privada", "kgstudio.treasury.contract-address": "Dirección del contrato", "kgstudio.treasury.deposit-tooltip": "¡Saldo inferior a {alert_threshold}{symbol}! Agregue fondos para garantizar el éxito de las transacciones.", "kgstudio.treasury.gas-swap.desc": "Gas Swap Desc", "kgstudio.treasury.gas-swap.margin": "Margen de beneficio de Gas Swap (%)", "kgstudio.treasury.gas-swap-title": "Intercambio de gas", "kgstudio.treasury.liquidity-settings": "Configuración de liquidez", "kgstudio.treasury.liquidity-settings-min-max": "Mín:{min}%, máximo:{max}%.", "kgstudio.treasury.liquidity-type": "Tipo de liquidez", "kgstudio.treasury.liquidity-update-confirm.btn": "Sí", "kgstudio.treasury.liquidity-update-confirm.message": "Todos sus clientes verán nuevas cotizaciones.", "kgstudio.treasury.liquidity-update-confirm.text": "¿Está seguro de actualizar el margen de beneficio?", "kgstudio.treasury.liquidity-update-success": "¡Actualizado correctamente!", "kgstudio.treasury.network": "Red", "kgstudio.treasury.price": "Precio", "kgstudio.treasury.profit-current-rate": "<PERSON><PERSON> de beneficio actual", "kgstudio.treasury.profit-description": "Descripción", "kgstudio.treasury.profit-margin": "<PERSON><PERSON> de beneficio", "kgstudio.treasury.profit-margin-desc": "Usa los precios dinámicos para calcular los pagos de los usuarios en función de los precios de mercado, las tarifas de procesamiento y los márgenes de beneficio.", "kgstudio.treasury.profit-margin-rate": "<PERSON><PERSON> de ma<PERSON> de beneficio", "kgstudio.treasury.profit-margin-setting": "Configuración del margen de beneficio", "kgstudio.treasury.profit-rate-edit": "Editar tasa de beneficio", "kgstudio.treasury.profit-rate-edit-title": "E<PERSON>cer <PERSON> de beneficio (%)", "kgstudio.treasury.profit-rate-setting": "Configuración de la tasa de beneficio", "kgstudio.treasury.profit-rate-update-success": "¡Actualizado correctamente!", "kgstudio.treasury.profit-rate-update-text": "¿Estás seguro de actualizar la tasa de beneficio?", "kgstudio.treasury.profit-service": "<PERSON><PERSON><PERSON>", "kgstudio.treasury.profit-service-bridge": "Bridge (cross-chain swap on DeFi)", "kgstudio.treasury.profit-service-bridge-desc1": "Cross-chain swap between Tron, Ethereum, Arbitrum, Polygon, Base, Optimism, and Binance Smart Chain. e.g. swap USDT(Tron) to ETH(Arbitrum), you will earn the source token USDT(Tron) as revenue.", "kgstudio.treasury.profit-service-bridge-desc2": "e.g. A customer paid some USDT(Tron) that equivalent to $100 USD, to cross-chain swap for ETH(Arbitrum). If the Business set Profit Margin at 2%, => the customer will get ETH(Arbitrum) that’s equivalent to 100* (1 - 2％) = 98 USD.", "kgstudio.treasury.profit-service-bridge-hint1": "You earn $n for every $100 volume the customer swaps.  if profit rate = 2%, n = (100) - 100* (1 - 2％) = 2 USD", "kgstudio.treasury.profit-service-bridge-info1": "Set the profit rate (%) for decentralized token swaps. This rate will reduce the amount of tokens the customer receives.", "kgstudio.treasury.profit-service-bridge-info2": "*Liquidity provided by decentralized services.", "kgstudio.treasury.profit-service-buy": "<PERSON><PERSON><PERSON>", "kgstudio.treasury.profit-service-buy-desc1": "Si estableces el margen de beneficio en el 1% y un cliente gasta 100 USD en la compra de criptomonedas\n=> Obtendrá 100/ (1+7+1%) = 92,59 U. Obtendrá USD como ingresos.", "kgstudio.treasury.profit-service-buy-desc2": "(el 1% es su margen de beneficio y el 7% es el costo de mercado del servicio de compra)", "kgstudio.treasury.profit-service-buy-hint1": "Establece la tasa de beneficio (%) para comprar fichas. Esta tasa se sumará al precio de mercado del token.", "kgstudio.treasury.profit-service-buy-info1": "Ganas n $ por cada 100$ que gaste el cliente.", "kgstudio.treasury.profit-service-buy-info2": "{profit_rate}{profit_usd}si tasa de beneficio ={profit_rate}%, n = 100/ (1 +7%) -100/ (1 +7% +%) = USD", "kgstudio.treasury.profit-service-send": "Enviar (with TRX)", "kgstudio.treasury.profit-service-send-batch": "Enviar (por lotes)", "kgstudio.treasury.profit-service-send-batch-desc1": "(El servicio de envío por lotes (colecta) solo cobra la tarifa de red inicial. No hay margen de beneficio que configurar ahora)", "kgstudio.treasury.profit-service-send-desc1": "Cuando un cliente envía un token de Tron a un amigo y este tiene suficiente TRX como token de gas, ganarás parte del token de gas (TRX) como ingreso. (El margen de beneficio de «Send» solo funciona en Tron).", "kgstudio.treasury.profit-service-send-desc2": "", "kgstudio.treasury.profit-service-send-gasless": "Enviar (Gasless)", "kgstudio.treasury.profit-service-send-gasless-desc1": "Cuando un cliente envía un token de TRON a un amigo sin tener suficiente TRX como token de gasolina, simplemente puede enviarle sus tokens TRC20 y tú ganarás parte del token de origen, por ejemplo, el USDT, como ingreso. (El margen de beneficio de «Gasless Send» solo funciona en Tron)", "kgstudio.treasury.profit-service-send-gasless-desc2": "Por ejemplo, un cliente quería enviar 12,5 USDT (20 TRC) a un amigo como pago de comida, y la tarifa inicial de gas era de 3 TRX. La empresa fijó el margen de beneficio en el 10%. \n=> El amigo recibirá 12,5 USDT, mientras que el cliente enviará 14,26 USDT en total. \n=> Para cubrir el costo inicial y toda la transacción, el contrato inteligente necesita 10 TRX como tarifa de gas. \n=> Tarifa por servicio de gas = 10 * (1 + 10%) = 11 TRX ~ 1.76 USD \n=> El cliente enviará 1,76 + 12,5 = 14,26 USDT.", "kgstudio.treasury.profit-service-send-gasless-hint1": "Establezca la tasa de beneficio (%) para los envíos sin gas. Esta tasa afectará a la cantidad real del token fuente enviado desde el monedero del cliente.", "kgstudio.treasury.profit-service-send-gasless-info1": "<PERSON>uede ganar n $ por cada transacción de Tron que envíe el cliente, según qué tan ocupada esté la red.", "kgstudio.treasury.profit-service-send-gasless-info2": "{profit_usd}si la tasa de beneficio es ={profit_rate}%, n = 10 *{profit_rate}% = {profit_trx}TRX ~ USD", "kgstudio.treasury.profit-service-send-hint1": "Establece la tasa de beneficio (%) para el envío de tokens. Esta tarifa aumentará la tarifa de gas que paga el cliente.", "kgstudio.treasury.profit-service-send-hint2": "*Solo se admiten transacciones entre TRC20 y USDT.", "kgstudio.treasury.profit-service-send-info1": "<PERSON>uede ganar n $ por cada transacción de Tron que envíe un cliente, según qué tan ocupada esté la red.", "kgstudio.treasury.profit-service-send-info2": "{profit_usd}si la tasa de beneficio es ={profit_rate}%, n = 3*{profit_rate}% = {profit_trx}TRX ~ USD", "kgstudio.treasury.profit-service-swap-cefi": "Intercambio (CeFi(AssetPro))", "kgstudio.treasury.profit-service-swap-cefi-desc1": "Solo para el token Tron (TRC20). Si un cliente presenta una solicitud de intercambio de gas (USDT (Tron) -> TRX)), obtendrás el token objetivo (TRX) como ingreso.", "kgstudio.treasury.profit-service-swap-cefi-desc2": "p. ej., un cliente pagó unos USDT equivalentes a 100 USD para cambiarlos por TRX. Y el coste del alquiler de energía de la cadena de bloques fue de 5 USD. Si la empresa fijó el margen de beneficio en el 10%, \n=> El cliente recibirá un TRX equivalente a (100 - 5) * (1 - 10%) = 85,5 USD.", "kgstudio.treasury.profit-service-swap-cefi-hint1": "Establezca la tasa de beneficio (%) para los intercambios de tokens centralizados. Esta tasa reducirá la cantidad de fichas que recibe el cliente.", "kgstudio.treasury.profit-service-swap-cefi-hint2": "*Liquidez proporcionada por su tesorería de AssetPro.", "kgstudio.treasury.profit-service-swap-cefi-info1": "Puede ganar n $ por cada volumen de 100$ que el cliente intercambie, según qué tan ocupada esté la red.", "kgstudio.treasury.profit-service-swap-cefi-info2": "si tasa de beneficio ={profit_rate}%, n = (100 - 5) - [(100 - 5) * (1 -{profit_rate}%)] = {profit_usd}USD", "kgstudio.treasury.profit-service-swap-defi": "Intercambio (DeFi)", "kgstudio.treasury.profit-service-swap-defi-desc1": "La liquidez proviene de un servicio DeFi de terceros y es compatible con Ethereum, Arbitrum, Polygon y Binance Smart Chain. <PERSON>r ejemplo, si cambias SUSHI por POL, obtendrás el valor en USD del token de origen (SUSHI) como ingreso.", "kgstudio.treasury.profit-service-swap-defi-desc2": "p. ej., un cliente pagó un SUSHI equivalente a 100 USD para cambiarlo por POL. Si la empresa establece un margen de beneficio del 10%, \n=> el cliente recibirá un POL equivalente a 100* (1 - 10%) = 90 USD.", "kgstudio.treasury.profit-service-swap-defi-hint1": "Establezca la tasa de beneficio (%) para los intercambios de tokens descentralizados. Aumentar esta tasa reducirá la cantidad de fichas que recibe el cliente.", "kgstudio.treasury.profit-service-swap-defi-hint2": "*Liquidez proporcionada por servicios descentralizados.", "kgstudio.treasury.profit-service-swap-defi-info1": "Ganas n $ por cada volumen de 100$ que el cliente intercambie.", "kgstudio.treasury.profit-service-swap-defi-info2": "{profit_usd}si tasa de beneficio ={profit_rate}%, n = (100) - 100* (1 -{profit_rate}%) = USD", "kgstudio.treasury.quantity": "Cantidad", "kgstudio.treasury.retrieve-balance-error": "", "kgstudio.treasury.reveal-seedphrase": "<PERSON><PERSON><PERSON> clave privada", "kgstudio.treasury.seedphrase-warning-1": "Su clave privada le da acceso total a su billetera y fondos.", "kgstudio.treasury.seedphrase-warning-2": "Nunca compartas tu clave privada con nadie ni la guardes digitalmente.", "kgstudio.treasury.seedphrase-warning-3": "KryptoGO no puede recuperar su clave privada o sus fondos en caso de pérdida o robo.", "kgstudio.treasury.seedphrase-warning-4": "KryptoGO no será responsable de ninguna pérdida resultante de la exposición de claves privadas.", "kgstudio.treasury.seedphrase-warning-title": "Advertencia de seguridad importante", "kgstudio.treasury.token": "Símbolo", "kgstudio.treasury.token-price": "Precio del token", "kgstudio.treasury.value": "Valor", "kgstudio.user360.wallet-usage-registered": "Activado", "kgstudio.user360.wallet-usage-unregistered": "Inactivado", "kgstudio.user-dna.7-day-active": "Activo durante 7 días", "kgstudio.user-dna.applied_at": "Aplicado en", "kgstudio.user-dna.app-open-times": "Horarios de apertura de la aplicación", "kgstudio.user-dna.approved": "Aprobado", "kgstudio.user-dna.approved_at": "Aprobado en", "kgstudio.user-dna.dapp-favorites": "Favoritos de DApp", "kgstudio.user-dna.delay-hint": "En ocasiones, la dirección del monedero y la información del saldo pueden sufrir retrasos debido al nivel de actividad en la cadena de bloques.", "kgstudio.user-dna.dob": "Fecha de nacimiento", "kgstudio.user-dna.email": "Correo electrónico", "kgstudio.user-dna.first-apply": "Primera solicitud", "kgstudio.user-dna.first-web3-activity": "Primera actividad de Web3", "kgstudio.user-dna.last-active": "Último activo", "kgstudio.user-dna.last-apply": "Última solicitud", "kgstudio.user-dna.last-login": "Último inicio de sesión", "kgstudio.user-dna.low": "<PERSON><PERSON>", "kgstudio.user-dna.name": "Nombre", "kgstudio.user-dna.nation": "Nacionalidad", "kgstudio.user-dna.national-id": "ID de país", "kgstudio.user-dna.non-kyc-user": "Usuario que no es de KYC", "kgstudio.user-dna.no-wallet": "Este cliente no tiene ningún monedero ahora.", "kgstudio.user-dna.of-10k-users": "de 10 000 usuarios", "kgstudio.user-dna.phone": "Teléfono", "kgstudio.user-dna.real-name": "Nombre real", "kgstudio.user-dna.registered": "Descargado e iniciado sesión", "kgstudio.user-dna.risk-score": "Puntuación de riesgo", "kgstudio.user-dna.sign-times": "Horarios de señalización", "kgstudio.user-dna.status": "Estado de KYC", "kgstudio.user-dna.submission": "Sumisión", "kgstudio.user-dna.title": "Perfil de usuario", "kgstudio.user-dna.transactions-volume": "Volumen de transacciones", "kgstudio.user-dna.tvl": "TVL", "kgstudio.user-dna.wallet-activity": "Actividad de monedero", "kgstudio.user-dna.wallet-app-activities": "Actividades de la aplicación Wallet", "kgstudio.user-dna.wallets": "carteras", "kgstudio.user-dna.wallet.tag": "Etiqueta", "kgstudio.validation.correct-format": "Introduce el formato correcto", "kgstudio.validation.number-greater-than-zero": "Introduce un valor superior a 0", "kgstudio.validation.phone-or-email-required": "Los fondos solo se pueden transferir a la dirección de correo electrónico o al número de teléfono que se haya registrado en su servicio de monedero.", "kgstudio.validation.required": "Introduce un valor", "kgstudio.validation.sorrect-format": "Introduce el formato correcto", "kgstudio.validation.valid-address": "Introduce una dirección de monedero válida.", "kgstudio.validation.valid-email": "Introduce un correo electrónico válido", "kgstudio.validation.valid-phone": "Introduce un número de teléfono válido", "kgstudio.wallet.active-features": "Funciones activas", "kgstudio.wallet.app-images.app-icon": "Icono de aplicación", "kgstudio.wallet.app-images.splash": "Splash", "kgstudio.wallet.app-images.title": "Imágenes de la aplicación", "kgstudio.wallet.app-settings": "Configuración de la aplicación", "kgstudio.wallet.app-under-review": "La aplicación está en revisión y no se puede modificar.", "kgstudio.wallet.button.view-demo": "Ver demostración", "kgstudio.wallet.config.android": "Android (Google Play)", "kgstudio.wallet.config.app-image": "Imagen de la aplicación", "kgstudio.wallet.config.app-startup": "Pantalla de inicio de la aplicación", "kgstudio.wallet.config.app-store-info": "Información de la tienda de aplicaciones", "kgstudio.wallet.config.brand-logo-alt": "logotipo de la marca", "kgstudio.wallet.config.check": "Comprobar", "kgstudio.wallet.config.completion.step1": "Abre KryptoGo Wallet, escanea el siguiente código QR para obtener una vista previa de tu aplicación Wallet", "kgstudio.wallet.config.completion.step2": "Si es necesario realizar ajustes, vuelve a los pasos anteriores para modificar la configuración", "kgstudio.wallet.config.completion.step3": "Después de confirmar que la configuración es correcta, haga clic en el botón de abajo para enviar", "kgstudio.wallet.config.completion.title": "¡Configuración de la aplicación completada!", "kgstudio.wallet.config.configure-later": "Configurar más tarde", "kgstudio.wallet.config.configure-publish-data": "Configurar la publicación de datos", "kgstudio.wallet.config.confirm-before-submit": "Antes de enviar, asegúrese de que todos los gráficos y textos sean correctos", "kgstudio.wallet.config.confirm-submit": "Confirma y envía la versión de producción de tu aplicación", "kgstudio.wallet.config.data-verification": "Verifica la información ingresada", "kgstudio.wallet.config.desc.all-chains-desc": "Todas las cadenas son compatibles con KryptoGO, incluidas Ethereum, Polygon, BNB Chain, Arbitrum, KCC, Ronin, Bitcoin, Solana, Tron. Cuando KryptoGO actualice las cadenas compatibles, tu cartera también se actualizará automáticamente.", "kgstudio.wallet.config.desc.all-evm-chains-desc": "Todas las cadenas EVM son compatibles con KryptoGO, incluidas Ethereum, Polygon, BNB Chain, Arbitrum, KCC y Ronin. Cuando KryptoGO actualice las cadenas EVM compatibles, tu cartera también se actualizará automáticamente.", "kgstudio.wallet.config.desc.currency-desc": "La página de inicio muestra el botón de función Swap, que permite a los usuarios intercambiar diferentes pares de operaciones (admite ETH, Polygon y BNB Single-Chain Swap).", "kgstudio.wallet.config.desc.custom-desc": "Elija las cadenas que desea mostrar de las compatibles con KryptoGO. Ten en cuenta que cuando KryptoGO actualice las cadenas compatibles, tu cartera no se actualizará automáticamente; tendrás que modificar la configuración tú mismo.", "kgstudio.wallet.config.desc.custom-token-description": "<PERSON>uedes a<PERSON>dir fichas personalizadas a la lista de fichas de la cartera y establecer el precio. Si no necesitas fichas personalizadas, puedes saltarte esta pregunta.", "kgstudio.wallet.config.desc.dapp-list": "Una vez que la aplicación esté activa, podrás actualizar tu lista de dApps en cualquier momento. Si actualmente no necesita personalizar la lista de dApps, simplemente omita esta.", "kgstudio.wallet.config.desc.defi-desc": "<PERSON><PERSON><PERSON>", "kgstudio.wallet.config.desc.displayed-asset-type-desc": "El tipo de activo es lo que se mostrará en la página de inicio de tu cartera.", "kgstudio.wallet.config.desc.displayed-chains-desc": "Las cadenas no seleccionadas no se admitirán ni se mostrarán en tu cartera.", "kgstudio.wallet.config.desc.english": "EN-US (predeterminado)", "kgstudio.wallet.config.desc.explore-dapp-browser-desc": "Los usuarios pueden conectarse a las operaciones de DApp a través del DApp Browser incorporado en la cartera.", "kgstudio.wallet.config.desc.japanese": "日本語", "kgstudio.wallet.config.desc.kyc-user-verification-desc": "En combinación con la función Compliance Pro, los usuarios pueden enviar datos de KYC y usted puede realizar una verificación de identidad y la diligencia debida sobre ellos.", "kgstudio.wallet.config.desc.languages": "Idioma de la aplicación Wallet", "kgstudio.wallet.config.desc.login-methods": "Método de inicio de sesión", "kgstudio.wallet.config.desc.nft-desc": "Soporta NFT en ETH y Polygon.", "kgstudio.wallet.config.desc.nft-rewards-desc": "Combínelo con la función NFT Boost, ofrezca a los usuarios la potenciación de NFT canjeable.", "kgstudio.wallet.config.desc.nft-sell-desc": "La página de NFT muestra el botón de venta, lo que permite a los usuarios incluir sus NFT en OpenSea con un solo clic (requiere la función DApp Browser).", "kgstudio.wallet.config.desc.show-poap-desc": "Los coleccionables muestran los NFT de POAP que son propiedad del usuario.", "kgstudio.wallet.config.desc.simplified-chinese": "中文（简体）", "kgstudio.wallet.config.desc.support-info": "El tipo de activo es lo que se mostrará en la página de inicio de tu cartera. Los tipos de activos no seleccionados no se mostrarán en las carteras de tus usuarios.", "kgstudio.wallet.config.desc.swap-desc": "La página de inicio muestra el botón de función Swap, que permite a los usuarios intercambiar diferentes fichas de cadena de bloques. (es compatible con ETH, Polygon, BNB y Arbitrum Single-Chain Swap). No contiene monedas Fiat.)", "kgstudio.wallet.config.desc.theme": "Aplica los colores de tu marca a la aplicación Wallet", "kgstudio.wallet.config.desc.traditional-chinese": "中文（繁體）", "kgstudio.wallet.config.design-guideline": "Guía de diseño de íconos de aplicaciones", "kgstudio.wallet.config.extension": "Extensión web (Chrome Store)", "kgstudio.wallet.config.file-type-supported": "Tipos de archivos compatibles: JPG, PNG. Tamaño máximo: 10 MB", "kgstudio.wallet.config.follow-guideline": "Por favor, sigue", "kgstudio.wallet.config.get-started-description": "La imagen que aparece en la pantalla de introducción. Se recomienda utilizar el logotipo de la marca", "kgstudio.wallet.config.get-started-title": "Imagen de inicio", "kgstudio.wallet.config.ios": "iOS (App Store)", "kgstudio.wallet.config.label.add": "AÑADIR", "kgstudio.wallet.config.label.all-chains": "Todas las cadenas", "kgstudio.wallet.config.label.all-evm-chains": "Todas las cadenas EVM", "kgstudio.wallet.config.label.currency": "Moneda (obligatorio)", "kgstudio.wallet.config.label.custom": "Personalizado", "kgstudio.wallet.config.label.custom-list": "Personaliza la lista de dApp", "kgstudio.wallet.config.label.custom-token": "Token personalizado", "kgstudio.wallet.config.label.default-list": "KryptoGo Selected (UniSwap, OpenSea, SushiSwap, <PERSON><PERSON><PERSON>, Dune y más.)", "kgstudio.wallet.config.label.defi": "<PERSON><PERSON><PERSON>", "kgstudio.wallet.config.label.email": "Correo electrónico", "kgstudio.wallet.config.label.explore-dapp-browser": "Explorar - <PERSON><PERSON><PERSON> Browser", "kgstudio.wallet.config.label.kyc-user-verification": "KYC: Verificación de usuario", "kgstudio.wallet.config.label.nft": "NFT", "kgstudio.wallet.config.label.nft-rewards": "Recompensas NFT", "kgstudio.wallet.config.label.nft-sell": "Venta de NFT（OpenSea）", "kgstudio.wallet.config.label.phone": "Teléfono", "kgstudio.wallet.config.label.preview": "Vista previa", "kgstudio.wallet.config.label.pro": "Pro", "kgstudio.wallet.config.label.show-poap": "Mostrar POAP", "kgstudio.wallet.config.label.swap": "Intercambio de criptomonedas", "kgstudio.wallet.config.processing-settings": "Espera, estamos procesando tu configuración", "kgstudio.wallet.config.promote-banner.desc": "Una vez que la aplicación esté activa, podrás actualizar tu Banner en cualquier momento. Si no necesitas configurar un banner ahora, omite esta pregunta.", "kgstudio.wallet.config.promote-banner.title": "Promocione el banner", "kgstudio.wallet.config.publish-settings-confirm-title": "Se confirmaron los datos de la configuración de publicación. ¿Desea presentar su solicitud para su producción ahora?", "kgstudio.wallet.config.publish-settings-description": "El equipo de KryptoGO creará inmediatamente su cartera en función de la configuración de la aplicación y la enviará para su revisión en la tienda de aplicaciones junto con los datos de publicación. Si todo va bien sin necesidad de revisiones, puede estar en la tienda en unos 14 días hábiles. (Si no se presenta para su producción ahora, haga clic en «Enviar» en la página del proyecto Wallet más adelante)", "kgstudio.wallet.config.recommended-size": "Tamaño recomendado: 1024 x 1024 px", "kgstudio.wallet.config.retry-or-contact": "Vuelva a intentarlo más tarde o póngase en contacto con el servicio de atención al cliente", "kgstudio.wallet.config.scanInstruction": "Abre el escáner de tu billetera KryptoGo y escanea el código QR para obtener una vista previa local", "kgstudio.wallet.config.scanToDemo.title1": "Escanear a demostración", "kgstudio.wallet.config.scanToDemo.title2": "en tu billetera KryptoGo", "kgstudio.wallet.config.shelf-platform-title": "Plataforma para lanzar", "kgstudio.wallet.config.splash-file-type-supported": "Tipos de archivos compatibles: JPG, PNG. Tamaño máximo: 10 MB", "kgstudio.wallet.config.splash-recommended-size": "Tamaño recomendado: 1080 x 1920 px", "kgstudio.wallet.config.splash-screen-title": "Pantalla de bienvenida", "kgstudio.wallet.config.steps.check": "Comprobar", "kgstudio.wallet.config.steps.contact": "Contacto", "kgstudio.wallet.config.steps.explorer": "<PERSON><PERSON><PERSON><PERSON>", "kgstudio.wallet.config.steps.explorer-hint": "Los usuarios pueden conectarse a varias DApps a través del Explorador. Puede personalizar la lista de dApps recomendadas a los usuarios y también añadir banners con fines publicitarios y de orientación. Tenga la seguridad de que, una vez que la aplicación se active, podrá cambiar esta configuración en cualquier momento.", "kgstudio.wallet.config.steps.feature": "Característica", "kgstudio.wallet.config.steps.theme": "<PERSON><PERSON>", "kgstudio.wallet.config.store-display-info": "Configura los datos de visualización de tu tienda de aplicaciones", "kgstudio.wallet.config.submission-description": "El equipo de KryptoGo creará inmediatamente tu cartera en función de la configuración de la aplicación y la enviará para que la revise en la tienda de aplicaciones. Si todo va bien sin necesidad de revisiones, puede estar en la tienda en unos 14 días hábiles.", "kgstudio.wallet.config.submission-failed": "¡Ha fallado el envío!", "kgstudio.wallet.config.submission-failed-description": "El envío ha fallado, inténtalo de nuevo más tarde", "kgstudio.wallet.config.submission-in-progress": "El envío de la aplicación Wallet está en curso...", "kgstudio.wallet.config.submission-in-progress-description": "<PERSON><PERSON><PERSON>, el envío está en curso...", "kgstudio.wallet.config.submission-successful": "¡Envío exitoso!", "kgstudio.wallet.config.submit-failed": "No se pudo enviar la configuración de la aplicación", "kgstudio.wallet.config.submit-later": "Enviar más tarde", "kgstudio.wallet.config.submit-success": "Se ha enviado la configuración de la aplicación. ¿Desea continuar con la configuración de publicación?", "kgstudio.wallet.config.submitting-app-settings": "Enviando la configuración de la aplicación...", "kgstudio.wallet.config.tabs.dex": "SEXO", "kgstudio.wallet.config.tabs.hot": "Caliente", "kgstudio.wallet.config.tabs.nft": "NFT", "kgstudio.wallet.config.title.dapp-list": "Lista de dApp", "kgstudio.wallet.config.title.displayed-asset-type": "Tipo de activo mostrado", "kgstudio.wallet.config.title.displayed-chains": "Cadenas de bloques mostradas", "kgstudio.wallet.config.title.enable-features": "Seleccione las funciones que desee activar", "kgstudio.wallet.config.title.help-center-url": "URL del centro de ayuda", "kgstudio.wallet.config.title.languages": "Idioma", "kgstudio.wallet.config.title.login-methods": "Métodos de inicio de sesión", "kgstudio.wallet.config.title.primary-color": "Color primario", "kgstudio.wallet.config.title.privacy-policy-url": "URL de la política de privacidad", "kgstudio.wallet.config.title.secondary-color": "Color secundario", "kgstudio.wallet.config.title.support-email": "Correo electrónico de soporte", "kgstudio.wallet.config.title.support-info": "Información de soporte", "kgstudio.wallet.config.title.terms-condition-url": "URL de términos y condiciones", "kgstudio.wallet.config.title.theme": "<PERSON><PERSON>", "kgstudio.wallet.customized-dapp-list": "Lista de dApp personalizada", "kgstudio.wallet.dex": "SEXO", "kgstudio.wallet.explorer-banner": "Banner Explorer", "kgstudio.wallet.extension.subtitle": "Tienda web de Chrome", "kgstudio.wallet.extension.title": "Extensión", "kgstudio.wallet.feature.explore-dapp": "Explorar - <PERSON><PERSON><PERSON> Browser", "kgstudio.wallet.feature.nft-rewards": "Recompensas NFT", "kgstudio.wallet.feature.nft-sell": "Venta de NFT", "kgstudio.wallet.feature-settings": "Configuración de funciones", "kgstudio.wallet.feature.show-kyc": "Mostrar KYC", "kgstudio.wallet.feature.show-poap": "Mostrar POAP", "kgstudio.wallet.feature.swap": "<PERSON><PERSON><PERSON>", "kgstudio.wallet.google-play.subtitle": "Google Play", "kgstudio.wallet.google-play.title": "Android", "kgstudio.wallet.help-center": "Centro de ayuda", "kgstudio.wallet.hot": "Caliente", "kgstudio.wallet.ios.subtitle": "Tienda de aplicaciones", "kgstudio.wallet.ios.title": "iOS", "kgstudio.wallet.language.vietnamese": "Tiếng <PERSON>", "kgstudio.wallet.mock.content": "Antes de enviarlo a las plataformas de aplicaciones, asegúrate de crear tu cuenta de desarrollador de la tienda de aplicaciones con el nombre de una organización. De acuerdo con la política de las tiendas de aplicaciones, los desarrolladores individuales no pueden publicar una aplicación de monedero de criptomonedas. Para obtener más información, visita la guía de publicación de las tiendas de aplicaciones.", "kgstudio.wallet.nft": "NFT", "kgstudio.wallet.privacy-policy": "Política de privacidad", "kgstudio.wallet.processing": "<PERSON><PERSON><PERSON>, estamos procesando su configuración.", "kgstudio.wallet.project-image": "Imagen del proyecto", "kgstudio.wallet.project-name": "Nombre del proyecto", "kgstudio.wallet.publish-settings": "Configuración de publicación", "kgstudio.wallet.setProjectTitle": "Establece tu proyecto de cartera", "kgstudio.wallet.status.draft": "<PERSON><PERSON><PERSON>", "kgstudio.wallet.status.in-review": "En revisión", "kgstudio.wallet.status.published": "Publicado", "kgstudio.wallet.supported-chains": "Cadenas compatibles", "kgstudio.wallet.supported-links": "Enlaces compatibles", "kgstudio.wallet.support-email": "Correo electrónico de soporte", "kgstudio.wallet.terms-condition": "Términos y condiciones", "kgstudio.wallet.theme.primary-color": "Color primario", "kgstudio.wallet.theme.secondary-color": "Color secundario", "kgstudio.wallet.theme.title": "<PERSON><PERSON>", "kgstudio.wallet.use-recommended-option": "Usa las opciones recomendadas", "page.page-size-description": "Mostrar {pageSize}filas por página", "permissions.notification-description": "Puede ver los usuarios que tienen permiso para acceder al sistema. Si desea editar sus usuarios, póngase en contacto con el proveedor del sistema.", "permissions.title": "Usuarios", "permissions.user-id": "ID de usuario", "send.by-email": "Email", "send.by-phone-number": "Por número de teléfono", "send.do-not-leave-page": "No salgas de esta página.", "send.loading-hint": "La transacción puede tardar más de lo esperado si la cadena de bloques está muy ocupada. Para comprobar el estado y los detalles de la transacción, haz clic en el hash de Tx.", "send.over-limit": "Por encima del límite", "send.remaining-balance": "Saldo transferible restante en la actualidad: ", "send.remaining-balance-today": "El saldo restante hoy es de {formattedCurrentLimit}U (con un límite diario de {formattedDailyLimit}U).", "send.send-confirm-alert": "Una vez que se confirme esta transacción, ¡se hará efectiva de inmediato y no se podrá revertir! Asegúrese de que la cadena de bloques, el importe y el destinatario sean correctos.", "send.send-to": "Enviar a", "send.title": "Enviar fondo", "send.to-user": "al usuario", "send.tx-failed": "Transacción fallida", "send.tx-failed-description": "No se pudo transferir los fondos. Vuelva a intentarlo o póngase en contacto con el administrador del sistema.", "send.tx-in-progress": "Transacción en curso.", "send.tx-success": "¡Transacción exitosa!", "transactions.recipient-placeholder": "Correo electrón<PERSON>, teléfono, dirección", "transactions.title": "Historial de transacciones", "transactions.user-placeholder": "ID, dirección", "validation.correct-format": "Introduce el formato correcto", "validation.number-greater-than-zero": "Introduce un valor superior a 0", "validation.phone-or-email-required": "Solo puede transferir fondos a los usuarios que se hayan registrado en esta cartera utilizando su «correo electrónico» o «número de teléfono».", "validation.required": "Introduce un valor", "validation.sorrect-format": "Introduce un formato válido.", "validation.valid-email": "Introduce un correo electrónico válido", "validation.valid-phone": "Introduce un número de teléfono válido"}