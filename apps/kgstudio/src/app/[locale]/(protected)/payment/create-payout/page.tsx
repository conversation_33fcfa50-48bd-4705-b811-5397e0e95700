'use client';

import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { showToast } from '@/2b/toast';
import { FormDropdown, FormInput, FormMediaUploader, FormTextarea } from '@/app/_common/components/form';
import { Media, useGcpFileUpload, usePageHeader } from '@/app/_common/hooks';
import { isApiError } from '@/app/_common/lib/api';
import { apiPaymentHooks } from '@/app/_common/services';
import { useOrganizationStore } from '@/app/_common/store';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button, Card, Form } from '@kryptogo/2b';
import { getChainIcon } from '@kryptogo/utils';

const CreatePayoutPage = () => {
  const t = useTranslations();
  const router = useRouter();
  const { orgId } = useOrganizationStore();
  const [isLoading, setIsLoading] = useState(false);
  usePageHeader({ title: t('kgstudio.payment.create-payout'), backLink: '/payment/payout-list' });

  // Create schema with translations
  const CreatePayoutSchema = z
    .object({
      // Recipient Details
      recipient_name: z.string().min(1, t('kgstudio.payment.validation.recipient-name-required')),
      recipient_email: z.string().email(t('kgstudio.payment.validation.email-valid')),

      // Your Details
      organization_name: z.string().min(1, t('kgstudio.payment.validation.organization-name-required')),
      organization_email: z.string().email(t('kgstudio.payment.validation.email-valid')),

      // Payout Details
      payout_id: z.string().optional(), // Auto-generated
      payout_due_date: z.string().min(1, t('kgstudio.payment.validation.due-date-required')),
      description: z.string().min(1, t('kgstudio.payment.validation.description-required')),
      attachment: z.any().optional(), // File upload

      // Payout Method
      network: z.enum(['arbitrum', 'base', 'optimism'], {
        required_error: t('kgstudio.payment.validation.network-required'),
      }),
      token: z.enum(['USDC', 'USDT'], {
        required_error: t('kgstudio.payment.validation.token-required'),
      }),
      recipient_wallet: z.string().min(1, t('kgstudio.payment.validation.recipient-wallet-required')),
      payout_amount: z.string().min(1, t('kgstudio.payment.validation.payout-amount-required')),
      currency: z.enum(['USD', 'TWD']).default('USD'),

      // Legacy fields for API compatibility
      pricing_mode: z.enum(['fiat', 'crypto']).default('crypto'),
      pay_token: z.enum(['USDC', 'USDT']).default('USDC'),
      payout_target_address: z.string(),
    })
    .refine((data) => {
      // Sync recipient_wallet with payout_target_address for API compatibility
      data.payout_target_address = data.recipient_wallet;
      data.pay_token = data.token;
      return true;
    });

  type CreatePayoutFormValues = z.infer<typeof CreatePayoutSchema>;
  type PayoutDetail = Omit<
    CreatePayoutFormValues,
    | 'network'
    | 'token'
    | 'recipient_wallet'
    | 'payout_amount'
    | 'currency'
    | 'pricing_mode'
    | 'pay_token'
    | 'payout_target_address'
    | 'payout_id'
  >;

  const form = useForm<CreatePayoutFormValues>({
    resolver: zodResolver(CreatePayoutSchema),
    defaultValues: {
      recipient_name: '',
      recipient_email: '',
      organization_name: '',
      organization_email: '',
      payout_id: `IV${Math.random().toString(36).substring(2, 11).toUpperCase()}`, // Auto-generate
      payout_due_date: '',
      description: '',
      network: 'arbitrum',
      token: 'USDC',
      recipient_wallet: '',
      payout_amount: '',
      currency: 'USD',
      pricing_mode: 'crypto',
      pay_token: 'USDC',
      payout_target_address: '',
    },
    mode: 'onChange',
  });

  const { uploadAllFiles } = useGcpFileUpload();

  const watchedValues = form.watch();
  const payoutAmount = parseFloat(watchedValues.payout_amount || '0');
  const handlingFee = payoutAmount * 0.05; // 5% handling fee
  const totalAmount = payoutAmount + handlingFee;

  const { mutate: createPaymentIntent } = apiPaymentHooks.useCreatePaymentIntent(
    {
      headers: {
        'X-Client-ID': 'kryptogo-xyz',
      },
    },
    {
      onSuccess: () => {
        showToast(t('kgstudio.payment.create-intent-success'), 'success');
        router.push('/payment/payout-list');
      },
      onError: (error) => {
        if (isApiError(error)) {
          showToast(error.message || t('kgstudio.payment.create-intent-error'), 'error');
        } else {
          showToast(t('kgstudio.payment.create-intent-error'), 'error');
        }
      },
    },
  );

  const onSubmit = async (data: CreatePayoutFormValues) => {
    if (!orgId) {
      showToast(t('kgstudio.payment.organization-required'), 'error');
      return;
    }

    setIsLoading(true);
    try {
      // Upload attachment first if it exists
      let attachmentUrls: string[] = [];
      if (data.attachment && data.attachment.length > 0) {
        const localFiles = data.attachment.filter((file: any) => !!file.file) as Media[];
        if (localFiles.length > 0) {
          const filesWithKey = {
            attachment: localFiles,
          };
          const uploadResults = await uploadAllFiles(filesWithKey, 'payment-payout');
          if (uploadResults && uploadResults.attachment && uploadResults.attachment.length > 0) {
            attachmentUrls = uploadResults.attachment;
          }
        }
      }

      // Map network to chain_id
      const chainIdMap = {
        arbitrum: 'arb',
        base: 'base',
        optimism: 'optimism',
      } as const;

      // Map the form data to the API request format
      const requestData = {
        chain_id: chainIdMap[data.network],
        amount: data.payout_amount,
        pricing_mode: 'crypto' as const,
        pay_token: data.token,
        payout_target_address: data.recipient_wallet,
        group_key: 'payout_no_1',
        order_data: {
          recipient_name: data.recipient_name,
          recipient_email: data.recipient_email,
          organization_name: data.organization_name,
          organization_email: data.organization_email,
          payout_due_date: data.payout_due_date,
          description: data.description,
          attachment: attachmentUrls,
        } satisfies PayoutDetail,
      };

      createPaymentIntent(requestData);
    } catch (error) {
      console.error('Error creating payout:', error);
      showToast(t('kgstudio.payment.create-intent-error'), 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    router.back();
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Main Content */}
      <div className="mx-auto max-w-4xl px-4 py-8 sm:px-6 lg:px-8">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              {/* Recipient Details Section */}
              <Card className="p-6">
                <div className="space-y-6">
                  <h2 className="text-xl font-semibold text-gray-900">{t('kgstudio.payment.recipient-details')}</h2>
                  <div className="grid grid-cols-1 gap-6">
                    <FormInput
                      control={form.control}
                      name="recipient_name"
                      title={t('kgstudio.payment.recipient-name')}
                      placeholder={t('kgstudio.payment.recipient-name-placeholder')}
                      required
                    />
                    <FormInput
                      control={form.control}
                      name="recipient_email"
                      title={t('kgstudio.payment.contact-email')}
                      placeholder={t('kgstudio.payment.contact-email-placeholder')}
                      type="email"
                      required
                    />
                  </div>
                </div>
              </Card>
              {/* Your Details Section */}
              <Card className="p-6">
                <div className="space-y-6">
                  <h2 className="text-xl font-semibold text-gray-900">{t('kgstudio.payment.your-details')}</h2>
                  <div className="grid grid-cols-1 gap-6">
                    <FormInput
                      control={form.control}
                      name="organization_name"
                      title={t('kgstudio.payment.your-organization-name')}
                      placeholder={t('kgstudio.payment.recipient-name-placeholder')}
                      required
                    />
                    <FormInput
                      control={form.control}
                      name="organization_email"
                      title={t('kgstudio.payment.your-organization-contact-email')}
                      placeholder={t('kgstudio.payment.contact-email-placeholder')}
                      type="email"
                      required
                    />
                  </div>
                </div>
              </Card>
            </div>

            {/* Payout Details Section */}
            <Card className="p-6">
              <div className="space-y-6">
                <h2 className="text-xl font-semibold text-gray-900">{t('kgstudio.payment.payout-details')}</h2>
                <div className="grid grid-cols-1 gap-6">
                  <FormInput
                    control={form.control}
                    name="payout_due_date"
                    title={t('kgstudio.payment.payout-due-date')}
                    placeholder={t('kgstudio.payment.payout-due-date-placeholder')}
                    type="date"
                    required
                  />
                </div>
                <FormTextarea
                  control={form.control}
                  name="description"
                  title={t('kgstudio.payment.description')}
                  placeholder={t('kgstudio.payment.description-placeholder')}
                  className="min-h-[120px]"
                  required
                />
                <div>
                  <label className="mb-2 block text-sm font-medium text-gray-700">
                    {t('kgstudio.payment.attachment-label')}
                  </label>
                  <FormMediaUploader
                    control={form.control}
                    name="attachment"
                    title=""
                    dropZoneDesc={t('kgstudio.payment.upload-attachment-desc')}
                    acceptType={['pdf', 'png', 'jpg', 'jpeg', 'webp']}
                    maxFileSize={10 * 1024 * 1024} // 10MB
                    dropZoneRatio="16/9"
                    dropZoneWidth={400}
                    previewInZone
                  />
                </div>
              </div>
            </Card>

            {/* Payout Method Section */}
            <Card className="p-6">
              <div className="space-y-6">
                <h2 className="text-xl font-semibold text-gray-900">{t('kgstudio.payment.payout-method')}</h2>
                <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                  <FormDropdown
                    control={form.control}
                    name="network"
                    title={t('kgstudio.payment.network')}
                    options={[
                      {
                        label: (
                          <div className="flex items-center gap-2">
                            <div className="relative h-5 w-5 overflow-hidden rounded-full">
                              <Image
                                src={getChainIcon('arbitrum') ?? ''}
                                alt="arbitrum"
                                fill
                                className="object-cover"
                              />
                            </div>
                            Arbitrum
                          </div>
                        ),
                        value: 'arbitrum',
                        labelText: 'Arbitrum',
                      },
                      {
                        label: (
                          <div className="flex items-center gap-2">
                            <div className="relative h-5 w-5 overflow-hidden rounded-full">
                              <Image src={getChainIcon('base') ?? ''} alt="base" fill className="object-cover" />
                            </div>
                            Base
                          </div>
                        ),
                        value: 'base',
                        labelText: 'Base',
                      },
                      {
                        label: (
                          <div className="flex items-center gap-2">
                            <div className="relative h-5 w-5 overflow-hidden rounded-full">
                              <Image
                                src={getChainIcon('optimism') ?? ''}
                                alt="optimism"
                                fill
                                className="object-cover"
                              />
                            </div>
                            Optimism
                          </div>
                        ),
                        value: 'optimism',
                        labelText: 'Optimism',
                      },
                    ]}
                    required
                  />
                  <FormDropdown
                    control={form.control}
                    name="token"
                    title={t('kgstudio.payment.token')}
                    options={[
                      {
                        label: (
                          <div className="flex items-center gap-2">
                            <div className="relative h-5 w-5 overflow-hidden rounded-full">
                              <Image
                                src="https://token-icons.s3.amazonaws.com/0xa0b86991c6218b36c1d19d4a2e9eb0ce3606eb48.png"
                                alt="USDC"
                                fill
                                className="object-cover"
                              />
                            </div>
                            USDC
                          </div>
                        ),
                        value: 'USDC',
                        labelText: 'USDC',
                      },
                      {
                        label: (
                          <div className="flex items-center gap-2">
                            <div className="relative h-5 w-5 overflow-hidden rounded-full">
                              <Image
                                src="https://token-icons.s3.amazonaws.com/0xdac17f958d2ee523a2206206994597c13d831ec7.png"
                                alt="USDT"
                                fill
                                className="object-cover"
                              />
                            </div>
                            USDT
                          </div>
                        ),
                        value: 'USDT',
                        labelText: 'USDT',
                      },
                    ]}
                    required
                  />
                </div>
                <FormInput
                  control={form.control}
                  name="recipient_wallet"
                  title={t('kgstudio.payment.recipient-receiving-wallet')}
                  placeholder={t('kgstudio.payment.recipient-wallet-placeholder')}
                  required
                />
                <div>
                  <label className="mb-2 block text-sm font-medium text-gray-700">
                    {t('kgstudio.payment.payout-amount')} <span className="text-red-500">*</span>
                  </label>
                  <div className="flex items-center gap-4">
                    {/* Left side - Input field with US flag */}
                    <div className="flex-1">
                      <div className="relative">
                        <div className="absolute left-3 top-1/2 flex -translate-y-1/2 items-center gap-2">
                          <div className="relative h-5 w-5 overflow-hidden rounded-full">
                            <Image src="https://flagcdn.com/w20/us.png" alt="USD" fill className="object-cover" />
                          </div>
                        </div>
                        <input
                          {...form.register('payout_amount')}
                          type="number"
                          step="0.01"
                          placeholder="2000"
                          className="w-full rounded-lg border border-gray-300 py-3 pl-12 pr-16 text-right focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                        />
                        <div className="absolute right-3 top-1/2 -translate-y-1/2 text-sm font-medium text-gray-600">
                          USD
                        </div>
                      </div>
                    </div>

                    {/* Equals sign */}
                    <div className="text-gray-400">≈</div>

                    {/* Right side - Display only field with token icon */}
                    <div className="flex-1">
                      <div className="relative">
                        <div className="absolute left-3 top-1/2 flex -translate-y-1/2 items-center gap-2">
                          <div className="flex h-6 w-6 items-center justify-center rounded-full bg-blue-500">
                            <span className="text-xs font-bold text-white">$</span>
                          </div>
                        </div>
                        <div className="w-full rounded-lg border border-gray-200 bg-gray-50 py-3 pl-12 pr-16 text-right text-gray-600">
                          {watchedValues.payout_amount || '2000'}
                        </div>
                        <div className="absolute right-3 top-1/2 -translate-y-1/2 text-sm font-medium text-gray-600">
                          {watchedValues.token}
                        </div>
                      </div>
                    </div>
                  </div>
                  {form.formState.errors.payout_amount && (
                    <p className="mt-1 text-sm text-red-600">{form.formState.errors.payout_amount.message}</p>
                  )}
                </div>
              </div>
            </Card>

            {/* Payout Summary Section */}
            <Card className="p-6">
              <div className="space-y-6">
                <h2 className="text-xl font-semibold text-gray-900">{t('kgstudio.payment.payout-summary')}</h2>
                <div className="space-y-4 rounded-lg bg-gray-50 p-6">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">{t('kgstudio.payment.payout-amount')}</span>
                    <span className="font-medium">
                      {payoutAmount.toFixed(2)} {watchedValues.currency}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">{t('kgstudio.payment.kryptogo-handling-fee')}</span>
                    <span className="font-medium">
                      {handlingFee.toFixed(2)} {watchedValues.currency}
                    </span>
                  </div>
                  <div className="border-t border-gray-200 pt-4">
                    <div className="flex items-center justify-between">
                      <span className="text-lg font-semibold">{t('kgstudio.payment.total-amount')}</span>
                      <span className="text-lg font-semibold">
                        {totalAmount.toFixed(2)} {watchedValues.currency}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </Card>

            {/* Form Actions */}
            <div className="flex justify-between border-t border-gray-200 pt-6">
              <Button type="button" variant="secondary" onClick={handleCancel} disabled={isLoading}>
                {t('kgstudio.common.cancel')}
              </Button>
              <Button type="submit" variant="primary" loading={isLoading}>
                {t('kgstudio.payment.create-payout')}
              </Button>
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
};

export default CreatePayoutPage;
